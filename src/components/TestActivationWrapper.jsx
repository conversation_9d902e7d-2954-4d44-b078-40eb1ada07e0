/**
 * Test Activation Wrapper Component
 * 
 * A simplified activation wrapper for testing that bypasses Supabase
 * and allows testing the activation UI without backend dependencies.
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../LanguageContext.jsx';
import ActivationPage from '../pages/ActivationPage';
import LicenseStatus from './LicenseStatus';
import './ActivationWrapper.css';

const TestActivationWrapper = ({ children }) => {
  const { currentLanguage, t } = useLanguage();
  const [activationState, setActivationState] = useState({
    isLoading: true,
    isActivated: false,
    showActivationDialog: false,
    showLicenseStatus: false,
    error: null
  });

  useEffect(() => {
    // Simulate initialization
    setTimeout(() => {
      // Check if there's a test activation in localStorage
      const testActivation = localStorage.getItem('test-activation');
      
      if (testActivation) {
        setActivationState({
          isLoading: false,
          isActivated: true,
          showActivationDialog: false,
          showLicenseStatus: false,
          error: null
        });
      } else {
        setActivationState({
          isLoading: false,
          isActivated: false,
          showActivationDialog: true,
          showLicenseStatus: false,
          error: null
        });
      }
    }, 1000);
  }, []);

  // t function is already available from useLanguage hook

  const handleActivationSuccess = (activationData) => {
    console.log('✅ Test activation successful:', activationData);
    
    // Save test activation to localStorage
    localStorage.setItem('test-activation', JSON.stringify({
      activated: true,
      activatedAt: new Date().toISOString(),
      testMode: true
    }));
    
    setActivationState({
      isLoading: false,
      isActivated: true,
      showActivationDialog: false,
      showLicenseStatus: false,
      error: null
    });
  };

  const handleShowLicenseStatus = () => {
    setActivationState(prev => ({
      ...prev,
      showLicenseStatus: true
    }));
  };

  const handleCloseLicenseStatus = () => {
    setActivationState(prev => ({
      ...prev,
      showLicenseStatus: false
    }));
  };

  const handleReactivate = () => {
    localStorage.removeItem('test-activation');
    setActivationState(prev => ({
      ...prev,
      isActivated: false,
      showActivationDialog: true,
      showLicenseStatus: false
    }));
  };

  // Loading state
  if (activationState.isLoading) {
    return (
      <div className="activation-loading">
        <div className="loading-container">
          <div className="loading-logo">
            <img src="/logosvg.svg" alt="iCalDZ" className="logo-image" />
          </div>
          <div className="loading-spinner"></div>
          <h2 className="loading-title">
            {t('initializingApp', 'جاري تهيئة التطبيق...')}
          </h2>
          <p className="loading-subtitle">
            {t('checkingActivation', 'فحص حالة التفعيل...')}
          </p>
          <div style={{ marginTop: '1rem', fontSize: '0.9rem', color: '#666' }}>
            Test Mode - No Supabase Required
          </div>
        </div>
      </div>
    );
  }

  // Not activated - show activation page
  if (!activationState.isActivated && activationState.showActivationDialog) {
    return (
      <TestActivationPage
        onActivationSuccess={handleActivationSuccess}
        onClose={() => {
          console.log('Test activation page close attempted');
        }}
      />
    );
  }

  // Activated - show main app with license status option
  if (activationState.isActivated) {
    return (
      <div className="activation-wrapper">
        {/* License Status Button */}
        <button 
          className="license-status-button"
          onClick={handleShowLicenseStatus}
          title={t('viewLicenseStatus', 'عرض حالة الترخيص')}
        >
          <span className="license-icon">🔐</span>
          <span className="license-text">{t('license', 'الترخيص')} (Test)</span>
        </button>

        {/* Main Application Content */}
        {children}

        {/* License Status Modal */}
        {activationState.showLicenseStatus && (
          <div className="license-status-modal">
            <div className="modal-overlay" onClick={handleCloseLicenseStatus}></div>
            <div className="modal-content">
              <div className="modal-header">
                <h3>{t('licenseInformation', 'معلومات الترخيص')} (Test Mode)</h3>
                <button 
                  className="modal-close"
                  onClick={handleCloseLicenseStatus}
                >
                  ×
                </button>
              </div>
              <div className="modal-body">
                <div style={{ padding: '2rem', textAlign: 'center' }}>
                  <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>✅</div>
                  <h3>Test Activation Active</h3>
                  <p>This is a test activation for development purposes.</p>
                  <button 
                    onClick={handleReactivate}
                    style={{
                      background: '#dc3545',
                      color: 'white',
                      border: 'none',
                      padding: '0.5rem 1rem',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      marginTop: '1rem'
                    }}
                  >
                    Clear Test Activation
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return null;
};

// Test Activation Page Component
const TestActivationPage = ({ onActivationSuccess }) => {
  const { currentLanguage, t } = useLanguage();
  const [activationCode, setActivationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    // Simulate activation process
    setTimeout(() => {
      if (activationCode.trim().toUpperCase().includes('TEST') || 
          activationCode.trim().toUpperCase().includes('ICAL') ||
          activationCode.trim().length > 10) {
        onActivationSuccess({
          activation_code: activationCode.trim().toUpperCase(),
          test_mode: true,
          activated_at: new Date().toISOString()
        });
      } else {
        setError('Please enter a test code (any code containing "TEST" or "ICAL" or longer than 10 characters)');
      }
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="activation-page" dir={currentLanguage === 'ar' ? 'rtl' : 'ltr'}>
      <div className="activation-container">
        <div className="activation-card">
          <div className="activation-step code-entry-step">
            <div className="step-header">
              <h2 className="step-title">
                {t('enterActivationCode', 'أدخل كود التفعيل')} (Test Mode)
              </h2>
            </div>

            <form onSubmit={handleSubmit} className="activation-form">
              <div className="form-section">
                <div className="form-group">
                  <label htmlFor="activationCode" className="form-label">
                    Test Activation Code
                  </label>
                  <input
                    type="text"
                    id="activationCode"
                    value={activationCode}
                    onChange={(e) => setActivationCode(e.target.value)}
                    placeholder="Enter any code containing 'TEST' or 'ICAL'"
                    className="form-input code-input"
                    required
                    disabled={isLoading}
                  />
                  <div className="input-hint">
                    Test Mode: Enter any code containing "TEST" or "ICAL" or longer than 10 characters
                  </div>
                </div>

                {error && (
                  <div className="error-card">
                    <div className="error-icon">⚠️</div>
                    <div className="error-content">
                      <div className="error-title">Test Error</div>
                      <div className="error-message">{error}</div>
                    </div>
                  </div>
                )}

                <button
                  type="submit"
                  className="btn-primary btn-large btn-submit"
                  disabled={isLoading || !activationCode.trim()}
                >
                  {isLoading ? (
                    <>
                      <div className="loading-spinner"></div>
                      Testing...
                    </>
                  ) : (
                    <>
                      Test Activate
                      <span className="btn-arrow">✓</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
        
        <div className="activation-footer">
          <div className="support-section">
            <p className="support-text">
              Test Mode - No Supabase Connection Required
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestActivationWrapper;
