import{C as e}from"./vendor-6d7f02bc.js";import"./react-vendor-7c7ffd2f.js";const t=new class{constructor(){this.securityKey="iCalDZ-Security-2025-v1.0",this.sessionKey="icaldz-security-session",this.isInitialized=!1,this.securityViolations=[],this.lastSecurityCheck=null,this.hardwareFingerprint=null}async initialize(){try{return console.log("🔒 Initializing web security service..."),this.hardwareFingerprint=await this.generateHardwareFingerprint(),this.isInitialized=!0,console.log("✅ Web security service initialized"),!0}catch(e){return console.error("❌ Failed to initialize web security service:",e),!1}}async generateHardwareFingerprint(){try{const i=[];"undefined"!=typeof window&&window.screen&&(i.push(`${window.screen.width}x${window.screen.height}`),i.push(`${window.screen.colorDepth}`));try{i.push(Intl.DateTimeFormat().resolvedOptions().timeZone)}catch(t){i.push((new Date).getTimezoneOffset().toString())}"undefined"!=typeof navigator&&i.push(navigator.language||navigator.userLanguage||"unknown"),"undefined"!=typeof navigator&&i.push(navigator.platform||"unknown"),"undefined"!=typeof navigator&&navigator.hardwareConcurrency&&i.push(navigator.hardwareConcurrency.toString()),"undefined"!=typeof navigator&&i.push(e.SHA256(navigator.userAgent).toString().substring(0,16));const r=i.join("|"),n=e.SHA256(r).toString();return console.log("🔍 Web hardware fingerprint generated"),n}catch(t){return console.error("❌ Failed to generate web hardware fingerprint:",t),e.SHA256("fallback-fingerprint-"+Date.now()).toString()}}handleSecurityViolation(e){const t={type:e,timestamp:(new Date).toISOString(),fingerprint:this.hardwareFingerprint};this.securityViolations.push(t),console.warn("🚨 Security violation detected:",e),this.securityViolations.length>50&&(this.securityViolations=this.securityViolations.slice(-25))}validateSession(){try{const t=localStorage.getItem(this.sessionKey);if(!t)return this.createNewSession();const i=e.AES.decrypt(t,this.securityKey),r=JSON.parse(i.toString(e.enc.Utf8)),n=Date.now()-new Date(r.created).getTime();return n>864e5?this.createNewSession():r.fingerprint===this.hardwareFingerprint||(this.handleSecurityViolation("Session fingerprint mismatch"),this.createNewSession())}catch(t){return console.error("❌ Session validation failed:",t),this.createNewSession()}}createNewSession(){try{const t={id:e.lib.WordArray.random(16).toString(),created:(new Date).toISOString(),fingerprint:this.hardwareFingerprint,violations:0},i=e.AES.encrypt(JSON.stringify(t),this.securityKey).toString();return localStorage.setItem(this.sessionKey,i),console.log("🔐 New web security session created"),!0}catch(t){return console.error("❌ Failed to create web security session:",t),!1}}getSecurityStatus(){return{isInitialized:this.isInitialized,hardwareFingerprint:this.hardwareFingerprint,violationCount:this.securityViolations.length,lastCheck:this.lastSecurityCheck,recentViolations:this.securityViolations.slice(-5)}}clearViolations(){this.securityViolations=[],console.log("🧹 Web security violations cleared")}};export{t as default,t as webSecurityService};
