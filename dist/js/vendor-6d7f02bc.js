import{g as e,a as t,c as r}from"./react-vendor-7c7ffd2f.js";var n={exports:{}},i={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(e){function t(e,t){var r=e.length;e.push(t);e:for(;0<r;){var n=r-1>>>1,o=e[n];if(!(0<i(o,t)))break e;e[n]=t,e[r]=o,r=n}}function r(e){return 0===e.length?null:e[0]}function n(e){if(0===e.length)return null;var t=e[0],r=e.pop();if(r!==t){e[0]=r;e:for(var n=0,o=e.length,s=o>>>1;n<s;){var a=2*(n+1)-1,c=e[a],u=a+1,l=e[u];if(0>i(c,r))u<o&&0>i(l,c)?(e[n]=l,e[u]=r,n=u):(e[n]=c,e[a]=r,n=a);else{if(!(u<o&&0>i(l,r)))break e;e[n]=l,e[u]=r,n=u}}}return t}function i(e,t){var r=e.sortIndex-t.sortIndex;return 0!==r?r:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var c=[],u=[],l=1,h=null,d=3,f=!1,p=!1,g=!1,v="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,_="undefined"!=typeof setImmediate?setImmediate:null;function b(e){for(var i=r(u);null!==i;){if(null===i.callback)n(u);else{if(!(i.startTime<=e))break;n(u),i.sortIndex=i.expirationTime,t(c,i)}i=r(u)}}function w(e){if(g=!1,b(e),!p)if(null!==r(c))p=!0,R(m);else{var t=r(u);null!==t&&B(w,t.startTime-e)}}function m(t,i){p=!1,g&&(g=!1,y(O),O=-1),f=!0;var o=d;try{for(b(i),h=r(c);null!==h&&(!(h.expirationTime>i)||t&&!P());){var s=h.callback;if("function"==typeof s){h.callback=null,d=h.priorityLevel;var a=s(h.expirationTime<=i);i=e.unstable_now(),"function"==typeof a?h.callback=a:h===r(c)&&n(c),b(i)}else n(c);h=r(c)}if(null!==h)var l=!0;else{var v=r(u);null!==v&&B(w,v.startTime-i),l=!1}return l}finally{h=null,d=o,f=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,E=!1,S=null,O=-1,x=5,T=-1;function P(){return!(e.unstable_now()-T<x)}function j(){if(null!==S){var t=e.unstable_now();T=t;var r=!0;try{r=S(!0,t)}finally{r?k():(E=!1,S=null)}}else E=!1}if("function"==typeof _)k=function(){_(j)};else if("undefined"!=typeof MessageChannel){var A=new MessageChannel,C=A.port2;A.port1.onmessage=j,k=function(){C.postMessage(null)}}else k=function(){v(j,0)};function R(e){S=e,E||(E=!0,k())}function B(t,r){O=v(function(){t(e.unstable_now())},r)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){p||f||(p=!0,R(m))},e.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):x=0<e?Math.floor(1e3/e):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return r(c)},e.unstable_next=function(e){switch(d){case 1:case 2:case 3:var t=3;break;default:t=d}var r=d;d=t;try{return e()}finally{d=r}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var r=d;d=e;try{return t()}finally{d=r}},e.unstable_scheduleCallback=function(n,i,o){var s=e.unstable_now();switch("object"==typeof o&&null!==o?o="number"==typeof(o=o.delay)&&0<o?s+o:s:o=s,n){case 1:var a=-1;break;case 2:a=250;break;case 5:a=1073741823;break;case 4:a=1e4;break;default:a=5e3}return n={id:l++,callback:i,priorityLevel:n,startTime:o,expirationTime:a=o+a,sortIndex:-1},o>s?(n.sortIndex=o,t(u,n),null===r(c)&&n===r(u)&&(g?(y(O),O=-1):g=!0,B(w,o-s))):(n.sortIndex=a,t(c,n),p||f||(p=!0,R(m))),n},e.unstable_shouldYield=P,e.unstable_wrapCallback=function(e){var t=d;return function(){var r=d;d=t;try{return e.apply(this,arguments)}finally{d=r}}}}(i),n.exports=i;var o=n.exports,s={},a={},c={};let u;const l=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];c.getSymbolSize=function(e){if(!e)throw new Error('"version" cannot be null or undefined');if(e<1||e>40)throw new Error('"version" should be in range from 1 to 40');return 4*e+17},c.getSymbolTotalCodewords=function(e){return l[e]},c.getBCHDigit=function(e){let t=0;for(;0!==e;)t++,e>>>=1;return t},c.setToSJISFunction=function(e){if("function"!=typeof e)throw new Error('"toSJISFunc" is not a valid function.');u=e},c.isKanjiModeEnabled=function(){return void 0!==u},c.toSJIS=function(e){return u(e)};var h,d={};function f(){this.buffer=[],this.length=0}(h=d).L={bit:1},h.M={bit:0},h.Q={bit:3},h.H={bit:2},h.isValid=function(e){return e&&void 0!==e.bit&&e.bit>=0&&e.bit<4},h.from=function(e,t){if(h.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"l":case"low":return h.L;case"m":case"medium":return h.M;case"q":case"quartile":return h.Q;case"h":case"high":return h.H;default:throw new Error("Unknown EC Level: "+e)}}(e)}catch(r){return t}},f.prototype={get:function(e){const t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)},put:function(e,t){for(let r=0;r<t;r++)this.putBit(1==(e>>>t-r-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var p=f;function g(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}g.prototype.set=function(e,t,r,n){const i=e*this.size+t;this.data[i]=r,n&&(this.reservedBit[i]=!0)},g.prototype.get=function(e,t){return this.data[e*this.size+t]},g.prototype.xor=function(e,t,r){this.data[e*this.size+t]^=r},g.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]};var v=g,y={};!function(e){const t=c.getSymbolSize;e.getRowColCoords=function(e){if(1===e)return[];const r=Math.floor(e/7)+2,n=t(e),i=145===n?26:2*Math.ceil((n-13)/(2*r-2)),o=[n-7];for(let t=1;t<r-1;t++)o[t]=o[t-1]-i;return o.push(6),o.reverse()},e.getPositions=function(t){const r=[],n=e.getRowColCoords(t),i=n.length;for(let e=0;e<i;e++)for(let t=0;t<i;t++)0===e&&0===t||0===e&&t===i-1||e===i-1&&0===t||r.push([n[e],n[t]]);return r}}(y);var _={};const b=c.getSymbolSize;_.getPositions=function(e){const t=b(e);return[[0,0],[t-7,0],[0,t-7]]};var w={};!function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t=3,r=3,n=40,i=10;function o(t,r,n){switch(t){case e.Patterns.PATTERN000:return(r+n)%2==0;case e.Patterns.PATTERN001:return r%2==0;case e.Patterns.PATTERN010:return n%3==0;case e.Patterns.PATTERN011:return(r+n)%3==0;case e.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(n/3))%2==0;case e.Patterns.PATTERN101:return r*n%2+r*n%3==0;case e.Patterns.PATTERN110:return(r*n%2+r*n%3)%2==0;case e.Patterns.PATTERN111:return(r*n%3+(r+n)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}}e.isValid=function(e){return null!=e&&""!==e&&!isNaN(e)&&e>=0&&e<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(e){const r=e.size;let n=0,i=0,o=0,s=null,a=null;for(let c=0;c<r;c++){i=o=0,s=a=null;for(let u=0;u<r;u++){let r=e.get(c,u);r===s?i++:(i>=5&&(n+=t+(i-5)),s=r,i=1),r=e.get(u,c),r===a?o++:(o>=5&&(n+=t+(o-5)),a=r,o=1)}i>=5&&(n+=t+(i-5)),o>=5&&(n+=t+(o-5))}return n},e.getPenaltyN2=function(e){const t=e.size;let n=0;for(let r=0;r<t-1;r++)for(let i=0;i<t-1;i++){const t=e.get(r,i)+e.get(r,i+1)+e.get(r+1,i)+e.get(r+1,i+1);4!==t&&0!==t||n++}return n*r},e.getPenaltyN3=function(e){const t=e.size;let r=0,i=0,o=0;for(let n=0;n<t;n++){i=o=0;for(let s=0;s<t;s++)i=i<<1&2047|e.get(n,s),s>=10&&(1488===i||93===i)&&r++,o=o<<1&2047|e.get(s,n),s>=10&&(1488===o||93===o)&&r++}return r*n},e.getPenaltyN4=function(e){let t=0;const r=e.data.length;for(let n=0;n<r;n++)t+=e.data[n];return Math.abs(Math.ceil(100*t/r/5)-10)*i},e.applyMask=function(e,t){const r=t.size;for(let n=0;n<r;n++)for(let i=0;i<r;i++)t.isReserved(i,n)||t.xor(i,n,o(e,i,n))},e.getBestMask=function(t,r){const n=Object.keys(e.Patterns).length;let i=0,o=1/0;for(let s=0;s<n;s++){r(s),e.applyMask(s,t);const n=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(s,t),n<o&&(o=n,i=s)}return i}}(w);var m={};const k=d,E=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],S=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];m.getBlocksCount=function(e,t){switch(t){case k.L:return E[4*(e-1)+0];case k.M:return E[4*(e-1)+1];case k.Q:return E[4*(e-1)+2];case k.H:return E[4*(e-1)+3];default:return}},m.getTotalCodewordsCount=function(e,t){switch(t){case k.L:return S[4*(e-1)+0];case k.M:return S[4*(e-1)+1];case k.Q:return S[4*(e-1)+2];case k.H:return S[4*(e-1)+3];default:return}};var O={},x={};const T=new Uint8Array(512),P=new Uint8Array(256);!function(){let e=1;for(let t=0;t<255;t++)T[t]=e,P[e]=t,e<<=1,256&e&&(e^=285);for(let t=255;t<512;t++)T[t]=T[t-255]}(),x.log=function(e){if(e<1)throw new Error("log("+e+")");return P[e]},x.exp=function(e){return T[e]},x.mul=function(e,t){return 0===e||0===t?0:T[P[e]+P[t]]},function(e){const t=x;e.mul=function(e,r){const n=new Uint8Array(e.length+r.length-1);for(let i=0;i<e.length;i++)for(let o=0;o<r.length;o++)n[i+o]^=t.mul(e[i],r[o]);return n},e.mod=function(e,r){let n=new Uint8Array(e);for(;n.length-r.length>=0;){const e=n[0];for(let o=0;o<r.length;o++)n[o]^=t.mul(r[o],e);let i=0;for(;i<n.length&&0===n[i];)i++;n=n.slice(i)}return n},e.generateECPolynomial=function(r){let n=new Uint8Array([1]);for(let i=0;i<r;i++)n=e.mul(n,new Uint8Array([1,t.exp(i)]));return n}}(O);const j=O;function A(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}A.prototype.initialize=function(e){this.degree=e,this.genPoly=j.generateECPolynomial(this.degree)},A.prototype.encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");const t=new Uint8Array(e.length+this.degree);t.set(e);const r=j.mod(t,this.genPoly),n=this.degree-r.length;if(n>0){const e=new Uint8Array(this.degree);return e.set(r,n),e}return r};var C=A,R={},B={},I={isValid:function(e){return!isNaN(e)&&e>=1&&e<=40}},M={};const L="[0-9]+";let $="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";$=$.replace(/u/g,"\\u");const D="(?:(?![A-Z0-9 $%*+\\-./:]|"+$+")(?:.|[\r\n]))+";M.KANJI=new RegExp($,"g"),M.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),M.BYTE=new RegExp(D,"g"),M.NUMERIC=new RegExp(L,"g"),M.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");const U=new RegExp("^"+$+"$"),N=new RegExp("^"+L+"$"),z=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");M.testKanji=function(e){return U.test(e)},M.testNumeric=function(e){return N.test(e)},M.testAlphanumeric=function(e){return z.test(e)},function(e){const t=I,r=M;e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(e,r){if(!e.ccBits)throw new Error("Invalid mode: "+e);if(!t.isValid(r))throw new Error("Invalid version: "+r);return r>=1&&r<10?e.ccBits[0]:r<27?e.ccBits[1]:e.ccBits[2]},e.getBestModeForData=function(t){return r.testNumeric(t)?e.NUMERIC:r.testAlphanumeric(t)?e.ALPHANUMERIC:r.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(e){if(e&&e.id)return e.id;throw new Error("Invalid mode")},e.isValid=function(e){return e&&e.bit&&e.ccBits},e.from=function(t,r){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+t)}}(t)}catch(n){return r}}}(B),function(e){const t=c,r=m,n=d,i=B,o=I,s=t.getBCHDigit(7973);function a(e,t){return i.getCharCountIndicator(e,t)+4}function u(e,t){let r=0;return e.forEach(function(e){const n=a(e.mode,t);r+=n+e.getBitsLength()}),r}e.from=function(e,t){return o.isValid(e)?parseInt(e,10):t},e.getCapacity=function(e,n,s){if(!o.isValid(e))throw new Error("Invalid QR Code version");void 0===s&&(s=i.BYTE);const c=8*(t.getSymbolTotalCodewords(e)-r.getTotalCodewordsCount(e,n));if(s===i.MIXED)return c;const u=c-a(s,e);switch(s){case i.NUMERIC:return Math.floor(u/10*3);case i.ALPHANUMERIC:return Math.floor(u/11*2);case i.KANJI:return Math.floor(u/13);case i.BYTE:default:return Math.floor(u/8)}},e.getBestVersionForData=function(t,r){let o;const s=n.from(r,n.M);if(Array.isArray(t)){if(t.length>1)return function(t,r){for(let n=1;n<=40;n++)if(u(t,n)<=e.getCapacity(n,r,i.MIXED))return n}(t,s);if(0===t.length)return 1;o=t[0]}else o=t;return function(t,r,n){for(let i=1;i<=40;i++)if(r<=e.getCapacity(i,n,t))return i}(o.mode,o.getLength(),s)},e.getEncodedBits=function(e){if(!o.isValid(e)||e<7)throw new Error("Invalid QR Code version");let r=e<<12;for(;t.getBCHDigit(r)-s>=0;)r^=7973<<t.getBCHDigit(r)-s;return e<<12|r}}(R);var H={};const F=c,G=F.getBCHDigit(1335);H.getEncodedBits=function(e,t){const r=e.bit<<3|t;let n=r<<10;for(;F.getBCHDigit(n)-G>=0;)n^=1335<<F.getBCHDigit(n)-G;return 21522^(r<<10|n)};var q={};const W=B;function K(e){this.mode=W.NUMERIC,this.data=e.toString()}K.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)},K.prototype.getLength=function(){return this.data.length},K.prototype.getBitsLength=function(){return K.getBitsLength(this.data.length)},K.prototype.write=function(e){let t,r,n;for(t=0;t+3<=this.data.length;t+=3)r=this.data.substr(t,3),n=parseInt(r,10),e.put(n,10);const i=this.data.length-t;i>0&&(r=this.data.substr(t),n=parseInt(r,10),e.put(n,3*i+1))};var J=K;const V=B,X=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function Y(e){this.mode=V.ALPHANUMERIC,this.data=e}Y.getBitsLength=function(e){return 11*Math.floor(e/2)+e%2*6},Y.prototype.getLength=function(){return this.data.length},Y.prototype.getBitsLength=function(){return Y.getBitsLength(this.data.length)},Y.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let r=45*X.indexOf(this.data[t]);r+=X.indexOf(this.data[t+1]),e.put(r,11)}this.data.length%2&&e.put(X.indexOf(this.data[t]),6)};var Q=Y;const Z=B;function ee(e){this.mode=Z.BYTE,this.data="string"==typeof e?(new TextEncoder).encode(e):new Uint8Array(e)}ee.getBitsLength=function(e){return 8*e},ee.prototype.getLength=function(){return this.data.length},ee.prototype.getBitsLength=function(){return ee.getBitsLength(this.data.length)},ee.prototype.write=function(e){for(let t=0,r=this.data.length;t<r;t++)e.put(this.data[t],8)};var te=ee;const re=B,ne=c;function ie(e){this.mode=re.KANJI,this.data=e}ie.getBitsLength=function(e){return 13*e},ie.prototype.getLength=function(){return this.data.length},ie.prototype.getBitsLength=function(){return ie.getBitsLength(this.data.length)},ie.prototype.write=function(e){let t;for(t=0;t<this.data.length;t++){let r=ne.toSJIS(this.data[t]);if(r>=33088&&r<=40956)r-=33088;else{if(!(r>=57408&&r<=60351))throw new Error("Invalid SJIS character: "+this.data[t]+"\nMake sure your charset is UTF-8");r-=49472}r=192*(r>>>8&255)+(255&r),e.put(r,13)}};var oe=ie,se={exports:{}};!function(e){var t={single_source_shortest_paths:function(e,r,n){var i={},o={};o[r]=0;var s,a,c,u,l,h,d,f=t.PriorityQueue.make();for(f.push(r,0);!f.empty();)for(c in a=(s=f.pop()).value,u=s.cost,l=e[a]||{})l.hasOwnProperty(c)&&(h=u+l[c],d=o[c],(void 0===o[c]||d>h)&&(o[c]=h,f.push(c,h),i[c]=a));if(void 0!==n&&void 0===o[n]){var p=["Could not find a path from ",r," to ",n,"."].join("");throw new Error(p)}return i},extract_shortest_path_from_predecessor_list:function(e,t){for(var r=[],n=t;n;)r.push(n),e[n],n=e[n];return r.reverse(),r},find_path:function(e,r,n){var i=t.single_source_shortest_paths(e,r,n);return t.extract_shortest_path_from_predecessor_list(i,n)},PriorityQueue:{make:function(e){var r,n=t.PriorityQueue,i={};for(r in e=e||{},n)n.hasOwnProperty(r)&&(i[r]=n[r]);return i.queue=[],i.sorter=e.sorter||n.default_sorter,i},default_sorter:function(e,t){return e.cost-t.cost},push:function(e,t){var r={value:e,cost:t};this.queue.push(r),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};e.exports=t}(se);var ae=se.exports;!function(e){const t=B,r=J,n=Q,i=te,o=oe,s=M,a=c,u=ae;function l(e){return unescape(encodeURIComponent(e)).length}function h(e,t,r){const n=[];let i;for(;null!==(i=e.exec(r));)n.push({data:i[0],index:i.index,mode:t,length:i[0].length});return n}function d(e){const r=h(s.NUMERIC,t.NUMERIC,e),n=h(s.ALPHANUMERIC,t.ALPHANUMERIC,e);let i,o;a.isKanjiModeEnabled()?(i=h(s.BYTE,t.BYTE,e),o=h(s.KANJI,t.KANJI,e)):(i=h(s.BYTE_KANJI,t.BYTE,e),o=[]);return r.concat(n,i,o).sort(function(e,t){return e.index-t.index}).map(function(e){return{data:e.data,mode:e.mode,length:e.length}})}function f(e,s){switch(s){case t.NUMERIC:return r.getBitsLength(e);case t.ALPHANUMERIC:return n.getBitsLength(e);case t.KANJI:return o.getBitsLength(e);case t.BYTE:return i.getBitsLength(e)}}function p(e,s){let c;const u=t.getBestModeForData(e);if(c=t.from(s,u),c!==t.BYTE&&c.bit<u.bit)throw new Error('"'+e+'" cannot be encoded with mode '+t.toString(c)+".\n Suggested mode is: "+t.toString(u));switch(c!==t.KANJI||a.isKanjiModeEnabled()||(c=t.BYTE),c){case t.NUMERIC:return new r(e);case t.ALPHANUMERIC:return new n(e);case t.KANJI:return new o(e);case t.BYTE:return new i(e)}}e.fromArray=function(e){return e.reduce(function(e,t){return"string"==typeof t?e.push(p(t,null)):t.data&&e.push(p(t.data,t.mode)),e},[])},e.fromString=function(r,n){const i=function(e){const r=[];for(let n=0;n<e.length;n++){const i=e[n];switch(i.mode){case t.NUMERIC:r.push([i,{data:i.data,mode:t.ALPHANUMERIC,length:i.length},{data:i.data,mode:t.BYTE,length:i.length}]);break;case t.ALPHANUMERIC:r.push([i,{data:i.data,mode:t.BYTE,length:i.length}]);break;case t.KANJI:r.push([i,{data:i.data,mode:t.BYTE,length:l(i.data)}]);break;case t.BYTE:r.push([{data:i.data,mode:t.BYTE,length:l(i.data)}])}}return r}(d(r,a.isKanjiModeEnabled())),o=function(e,r){const n={},i={start:{}};let o=["start"];for(let s=0;s<e.length;s++){const a=e[s],c=[];for(let e=0;e<a.length;e++){const u=a[e],l=""+s+e;c.push(l),n[l]={node:u,lastCount:0},i[l]={};for(let e=0;e<o.length;e++){const s=o[e];n[s]&&n[s].node.mode===u.mode?(i[s][l]=f(n[s].lastCount+u.length,u.mode)-f(n[s].lastCount,u.mode),n[s].lastCount+=u.length):(n[s]&&(n[s].lastCount=u.length),i[s][l]=f(u.length,u.mode)+4+t.getCharCountIndicator(u.mode,r))}}o=c}for(let t=0;t<o.length;t++)i[o[t]].end=0;return{map:i,table:n}}(i,n),s=u.find_path(o.map,"start","end"),c=[];for(let e=1;e<s.length-1;e++)c.push(o.table[s[e]].node);return e.fromArray(function(e){return e.reduce(function(e,t){const r=e.length-1>=0?e[e.length-1]:null;return r&&r.mode===t.mode?(e[e.length-1].data+=t.data,e):(e.push(t),e)},[])}(c))},e.rawSplit=function(t){return e.fromArray(d(t,a.isKanjiModeEnabled()))}}(q);const ce=c,ue=d,le=p,he=v,de=y,fe=_,pe=w,ge=m,ve=C,ye=R,_e=H,be=B,we=q;function me(e,t,r){const n=e.size,i=_e.getEncodedBits(t,r);let o,s;for(o=0;o<15;o++)s=1==(i>>o&1),o<6?e.set(o,8,s,!0):o<8?e.set(o+1,8,s,!0):e.set(n-15+o,8,s,!0),o<8?e.set(8,n-o-1,s,!0):o<9?e.set(8,15-o-1+1,s,!0):e.set(8,15-o-1,s,!0);e.set(n-8,8,1,!0)}function ke(e,t,r){const n=new le;r.forEach(function(t){n.put(t.mode.bit,4),n.put(t.getLength(),be.getCharCountIndicator(t.mode,e)),t.write(n)});const i=8*(ce.getSymbolTotalCodewords(e)-ge.getTotalCodewordsCount(e,t));for(n.getLengthInBits()+4<=i&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(0);const o=(i-n.getLengthInBits())/8;for(let s=0;s<o;s++)n.put(s%2?17:236,8);return function(e,t,r){const n=ce.getSymbolTotalCodewords(t),i=ge.getTotalCodewordsCount(t,r),o=n-i,s=ge.getBlocksCount(t,r),a=n%s,c=s-a,u=Math.floor(n/s),l=Math.floor(o/s),h=l+1,d=u-l,f=new ve(d);let p=0;const g=new Array(s),v=new Array(s);let y=0;const _=new Uint8Array(e.buffer);for(let E=0;E<s;E++){const e=E<c?l:h;g[E]=_.slice(p,p+e),v[E]=f.encode(g[E]),p+=e,y=Math.max(y,e)}const b=new Uint8Array(n);let w,m,k=0;for(w=0;w<y;w++)for(m=0;m<s;m++)w<g[m].length&&(b[k++]=g[m][w]);for(w=0;w<d;w++)for(m=0;m<s;m++)b[k++]=v[m][w];return b}(n,e,t)}function Ee(e,t,r,n){let i;if(Array.isArray(e))i=we.fromArray(e);else{if("string"!=typeof e)throw new Error("Invalid data");{let n=t;if(!n){const t=we.rawSplit(e);n=ye.getBestVersionForData(t,r)}i=we.fromString(e,n||40)}}const o=ye.getBestVersionForData(i,r);if(!o)throw new Error("The amount of data is too big to be stored in a QR Code");if(t){if(t<o)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+o+".\n")}else t=o;const s=ke(t,r,i),a=ce.getSymbolSize(t),c=new he(a);return function(e,t){const r=e.size,n=fe.getPositions(t);for(let i=0;i<n.length;i++){const t=n[i][0],o=n[i][1];for(let n=-1;n<=7;n++)if(!(t+n<=-1||r<=t+n))for(let i=-1;i<=7;i++)o+i<=-1||r<=o+i||(n>=0&&n<=6&&(0===i||6===i)||i>=0&&i<=6&&(0===n||6===n)||n>=2&&n<=4&&i>=2&&i<=4?e.set(t+n,o+i,!0,!0):e.set(t+n,o+i,!1,!0))}}(c,t),function(e){const t=e.size;for(let r=8;r<t-8;r++){const t=r%2==0;e.set(r,6,t,!0),e.set(6,r,t,!0)}}(c),function(e,t){const r=de.getPositions(t);for(let n=0;n<r.length;n++){const t=r[n][0],i=r[n][1];for(let r=-2;r<=2;r++)for(let n=-2;n<=2;n++)-2===r||2===r||-2===n||2===n||0===r&&0===n?e.set(t+r,i+n,!0,!0):e.set(t+r,i+n,!1,!0)}}(c,t),me(c,r,0),t>=7&&function(e,t){const r=e.size,n=ye.getEncodedBits(t);let i,o,s;for(let a=0;a<18;a++)i=Math.floor(a/3),o=a%3+r-8-3,s=1==(n>>a&1),e.set(i,o,s,!0),e.set(o,i,s,!0)}(c,t),function(e,t){const r=e.size;let n=-1,i=r-1,o=7,s=0;for(let a=r-1;a>0;a-=2)for(6===a&&a--;;){for(let r=0;r<2;r++)if(!e.isReserved(i,a-r)){let n=!1;s<t.length&&(n=1==(t[s]>>>o&1)),e.set(i,a-r,n),o--,-1===o&&(s++,o=7)}if(i+=n,i<0||r<=i){i-=n,n=-n;break}}}(c,s),isNaN(n)&&(n=pe.getBestMask(c,me.bind(null,c,r))),pe.applyMask(n,c),me(c,r,n),{modules:c,version:t,errorCorrectionLevel:r,maskPattern:n,segments:i}}a.create=function(e,t){if(void 0===e||""===e)throw new Error("No input text");let r,n,i=ue.M;return void 0!==t&&(i=ue.from(t.errorCorrectionLevel,ue.M),r=ye.from(t.version),n=pe.from(t.maskPattern),t.toSJISFunc&&ce.setToSJISFunction(t.toSJISFunc)),Ee(e,r,i,n)};var Se={},Oe={};!function(e){function t(e){if("number"==typeof e&&(e=e.toString()),"string"!=typeof e)throw new Error("Color should be defined as hex string");let t=e.slice().replace("#","").split("");if(t.length<3||5===t.length||t.length>8)throw new Error("Invalid hex color: "+e);3!==t.length&&4!==t.length||(t=Array.prototype.concat.apply([],t.map(function(e){return[e,e]}))),6===t.length&&t.push("F","F");const r=parseInt(t.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+t.slice(0,6).join("")}}e.getOptions=function(e){e||(e={}),e.color||(e.color={});const r=void 0===e.margin||null===e.margin||e.margin<0?4:e.margin,n=e.width&&e.width>=21?e.width:void 0,i=e.scale||4;return{width:n,scale:n?4:i,margin:r,color:{dark:t(e.color.dark||"#000000ff"),light:t(e.color.light||"#ffffffff")},type:e.type,rendererOpts:e.rendererOpts||{}}},e.getScale=function(e,t){return t.width&&t.width>=e+2*t.margin?t.width/(e+2*t.margin):t.scale},e.getImageWidth=function(t,r){const n=e.getScale(t,r);return Math.floor((t+2*r.margin)*n)},e.qrToImageData=function(t,r,n){const i=r.modules.size,o=r.modules.data,s=e.getScale(i,n),a=Math.floor((i+2*n.margin)*s),c=n.margin*s,u=[n.color.light,n.color.dark];for(let e=0;e<a;e++)for(let r=0;r<a;r++){let l=4*(e*a+r),h=n.color.light;if(e>=c&&r>=c&&e<a-c&&r<a-c){h=u[o[Math.floor((e-c)/s)*i+Math.floor((r-c)/s)]?1:0]}t[l++]=h.r,t[l++]=h.g,t[l++]=h.b,t[l]=h.a}}}(Oe),function(e){const t=Oe;e.render=function(e,r,n){let i=n,o=r;void 0!==i||r&&r.getContext||(i=r,r=void 0),r||(o=function(){try{return document.createElement("canvas")}catch(e){throw new Error("You need to specify a canvas element")}}()),i=t.getOptions(i);const s=t.getImageWidth(e.modules.size,i),a=o.getContext("2d"),c=a.createImageData(s,s);return t.qrToImageData(c.data,e,i),function(e,t,r){e.clearRect(0,0,t.width,t.height),t.style||(t.style={}),t.height=r,t.width=r,t.style.height=r+"px",t.style.width=r+"px"}(a,o,s),a.putImageData(c,0,0),o},e.renderToDataURL=function(t,r,n){let i=n;void 0!==i||r&&r.getContext||(i=r,r=void 0),i||(i={});const o=e.render(t,r,i),s=i.type||"image/png",a=i.rendererOpts||{};return o.toDataURL(s,a.quality)}}(Se);var xe={};const Te=Oe;function Pe(e,t){const r=e.a/255,n=t+'="'+e.hex+'"';return r<1?n+" "+t+'-opacity="'+r.toFixed(2).slice(1)+'"':n}function je(e,t,r){let n=e+t;return void 0!==r&&(n+=" "+r),n}xe.render=function(e,t,r){const n=Te.getOptions(t),i=e.modules.size,o=e.modules.data,s=i+2*n.margin,a=n.color.light.a?"<path "+Pe(n.color.light,"fill")+' d="M0 0h'+s+"v"+s+'H0z"/>':"",c="<path "+Pe(n.color.dark,"stroke")+' d="'+function(e,t,r){let n="",i=0,o=!1,s=0;for(let a=0;a<e.length;a++){const c=Math.floor(a%t),u=Math.floor(a/t);c||o||(o=!0),e[a]?(s++,a>0&&c>0&&e[a-1]||(n+=o?je("M",c+r,.5+u+r):je("m",i,0),i=0,o=!1),c+1<t&&e[a+1]||(n+=je("h",s),s=0)):i++}return n}(o,i,n.margin)+'"/>',u='viewBox="0 0 '+s+" "+s+'"',l='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+u+' shape-rendering="crispEdges">'+a+c+"</svg>\n";return"function"==typeof r&&r(null,l),l};const Ae=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then},Ce=a,Re=Se,Be=xe;function Ie(e,t,r,n,i){const o=[].slice.call(arguments,1),s=o.length,a="function"==typeof o[s-1];if(!a&&!Ae())throw new Error("Callback required as last argument");if(!a){if(s<1)throw new Error("Too few arguments provided");return 1===s?(r=t,t=n=void 0):2!==s||t.getContext||(n=r,r=t,t=void 0),new Promise(function(i,o){try{const o=Ce.create(r,n);i(e(o,t,n))}catch(s){o(s)}})}if(s<2)throw new Error("Too few arguments provided");2===s?(i=r,r=t,t=n=void 0):3===s&&(t.getContext&&void 0===i?(i=n,n=void 0):(i=n,n=r,r=t,t=void 0));try{const o=Ce.create(r,n);i(null,e(o,t,n))}catch(c){i(c)}}s.create=Ce.create,s.toCanvas=Ie.bind(null,Re.render),s.toDataURL=Ie.bind(null,Re.renderToDataURL),s.toString=Ie.bind(null,function(e,t,r){return Be.render(e,r)});var Me={},Le={},$e={};Object.defineProperty($e,"__esModule",{value:!0});$e.default=function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.data=t,this.text=r.text||t,this.options=r},Object.defineProperty(Le,"__esModule",{value:!0}),Le.CODE39=void 0;var De,Ue=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Ne=(De=$e)&&De.__esModule?De:{default:De};var ze=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=t.toUpperCase(),r.mod43&&(t+=function(e){return He[e]}(function(e){for(var t=0,r=0;r<e.length;r++)t+=qe(e[r]);return t%=43,t}(t))),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Ne.default),Ue(e,[{key:"encode",value:function(){for(var e=Ge("*"),t=0;t<this.data.length;t++)e+=Ge(this.data[t])+"0";return{data:e+=Ge("*"),text:this.text}}},{key:"valid",value:function(){return-1!==this.data.search(/^[0-9A-Z\-\.\ \$\/\+\%]+$/)}}]),e}(),He=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","-","."," ","$","/","+","%","*"],Fe=[20957,29783,23639,30485,20951,29813,23669,20855,29789,23645,29975,23831,30533,22295,30149,24005,21623,29981,23837,22301,30023,23879,30545,22343,30161,24017,21959,30065,23921,22385,29015,18263,29141,17879,29045,18293,17783,29021,18269,17477,17489,17681,20753,35770];function Ge(e){return function(e){return Fe[e].toString(2)}(qe(e))}function qe(e){return He.indexOf(e)}Le.CODE39=ze;var We,Ke={},Je={},Ve={},Xe={};function Ye(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Object.defineProperty(Xe,"__esModule",{value:!0});var Qe=Xe.SET_A=0,Ze=Xe.SET_B=1,et=Xe.SET_C=2;Xe.SHIFT=98;var tt=Xe.START_A=103,rt=Xe.START_B=104,nt=Xe.START_C=105;Xe.MODULO=103,Xe.STOP=106,Xe.FNC1=207,Xe.SET_BY_CODE=(Ye(We={},tt,Qe),Ye(We,rt,Ze),Ye(We,nt,et),We),Xe.SWAP={101:Qe,100:Ze,99:et},Xe.A_START_CHAR=String.fromCharCode(208),Xe.B_START_CHAR=String.fromCharCode(209),Xe.C_START_CHAR=String.fromCharCode(210),Xe.A_CHARS="[\0-_È-Ï]",Xe.B_CHARS="[ -È-Ï]",Xe.C_CHARS="(Ï*[0-9]{2}Ï*)",Xe.BARS=[11011001100,11001101100,11001100110,10010011e3,10010001100,10001001100,10011001e3,10011000100,10001100100,11001001e3,11001000100,11000100100,10110011100,10011011100,10011001110,10111001100,10011101100,10011100110,11001110010,11001011100,11001001110,11011100100,11001110100,11101101110,11101001100,11100101100,11100100110,11101100100,11100110100,11100110010,11011011e3,11011000110,11000110110,10100011e3,10001011e3,10001000110,10110001e3,10001101e3,10001100010,11010001e3,11000101e3,11000100010,10110111e3,10110001110,10001101110,10111011e3,10111000110,10001110110,11101110110,11010001110,11000101110,11011101e3,11011100010,11011101110,11101011e3,11101000110,11100010110,11101101e3,11101100010,11100011010,11101111010,11001000010,11110001010,1010011e4,10100001100,1001011e4,10010000110,10000101100,10000100110,1011001e4,10110000100,1001101e4,10011000010,10000110100,10000110010,11000010010,1100101e4,11110111010,11000010100,10001111010,10100111100,10010111100,10010011110,10111100100,10011110100,10011110010,11110100100,11110010100,11110010010,11011011110,11011110110,11110110110,10101111e3,10100011110,10001011110,10111101e3,10111100010,11110101e3,11110100010,10111011110,10111101110,11101011110,11110101110,11010000100,1101001e4,11010011100,1100011101011],Object.defineProperty(Ve,"__esModule",{value:!0});var it=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),ot=function(e){return e&&e.__esModule?e:{default:e}}($e),st=Xe;var at=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t.substring(1),r));return n.bytes=t.split("").map(function(e){return e.charCodeAt(0)}),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,ot.default),it(e,[{key:"valid",value:function(){return/^[\x00-\x7F\xC8-\xD3]+$/.test(this.data)}},{key:"encode",value:function(){var t=this.bytes,r=t.shift()-105,n=st.SET_BY_CODE[r];if(void 0===n)throw new RangeError("The encoding does not start with a start character.");!0===this.shouldEncodeAsEan128()&&t.unshift(st.FNC1);var i=e.next(t,1,n);return{text:this.text===this.data?this.text.replace(/[^\x20-\x7E]/g,""):this.text,data:e.getBar(r)+i.result+e.getBar((i.checksum+r)%st.MODULO)+e.getBar(st.STOP)}}},{key:"shouldEncodeAsEan128",value:function(){var e=this.options.ean128||!1;return"string"==typeof e&&(e="true"===e.toLowerCase()),e}}],[{key:"getBar",value:function(e){return st.BARS[e]?st.BARS[e].toString():""}},{key:"correctIndex",value:function(e,t){if(t===st.SET_A){var r=e.shift();return r<32?r+64:r-32}return t===st.SET_B?e.shift()-32:10*(e.shift()-48)+e.shift()-48}},{key:"next",value:function(t,r,n){if(!t.length)return{result:"",checksum:0};var i=void 0,o=void 0;if(t[0]>=200){o=t.shift()-105;var s=st.SWAP[o];void 0!==s?i=e.next(t,r+1,s):(n!==st.SET_A&&n!==st.SET_B||o!==st.SHIFT||(t[0]=n===st.SET_A?t[0]>95?t[0]-96:t[0]:t[0]<32?t[0]+96:t[0]),i=e.next(t,r+1,n))}else o=e.correctIndex(t,n),i=e.next(t,r+1,n);var a=o*r;return{result:e.getBar(o)+i.result,checksum:a+i.checksum}}}]),e}();Ve.default=at;var ct={};Object.defineProperty(ct,"__esModule",{value:!0});var ut=Xe,lt=function(e){return e.match(new RegExp("^"+ut.A_CHARS+"*"))[0].length},ht=function(e){return e.match(new RegExp("^"+ut.B_CHARS+"*"))[0].length},dt=function(e){return e.match(new RegExp("^"+ut.C_CHARS+"*"))[0]};function ft(e,t){var r=t?ut.A_CHARS:ut.B_CHARS,n=e.match(new RegExp("^("+r+"+?)(([0-9]{2}){2,})([^0-9]|$)"));if(n)return n[1]+String.fromCharCode(204)+pt(e.substring(n[1].length));var i=e.match(new RegExp("^"+r+"+"))[0];return i.length===e.length?e:i+String.fromCharCode(t?205:206)+ft(e.substring(i.length),!t)}function pt(e){var t=dt(e),r=t.length;if(r===e.length)return e;e=e.substring(r);var n=lt(e)>=ht(e);return t+String.fromCharCode(n?206:205)+ft(e,n)}ct.default=function(e){var t=void 0;if(dt(e).length>=2)t=ut.C_START_CHAR+pt(e);else{var r=lt(e)>ht(e);t=(r?ut.A_START_CHAR:ut.B_START_CHAR)+ft(e,r)}return t.replace(/[\xCD\xCE]([^])[\xCD\xCE]/,function(e,t){return String.fromCharCode(203)+t})},Object.defineProperty(Je,"__esModule",{value:!0});var gt=yt(Ve),vt=yt(ct);function yt(e){return e&&e.__esModule?e:{default:e}}function _t(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var bt=function(){function e(t,r){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),/^[\x00-\x7F\xC8-\xD3]+$/.test(t))var n=_t(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,(0,vt.default)(t),r));else n=_t(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r));return _t(n)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,gt.default),e}();Je.default=bt;var wt={};Object.defineProperty(wt,"__esModule",{value:!0});var mt=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),kt=function(e){return e&&e.__esModule?e:{default:e}}(Ve),Et=Xe;var St=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,Et.A_START_CHAR+t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,kt.default),mt(e,[{key:"valid",value:function(){return new RegExp("^"+Et.A_CHARS+"+$").test(this.data)}}]),e}();wt.default=St;var Ot={};Object.defineProperty(Ot,"__esModule",{value:!0});var xt=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Tt=function(e){return e&&e.__esModule?e:{default:e}}(Ve),Pt=Xe;var jt=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,Pt.B_START_CHAR+t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Tt.default),xt(e,[{key:"valid",value:function(){return new RegExp("^"+Pt.B_CHARS+"+$").test(this.data)}}]),e}();Ot.default=jt;var At={};Object.defineProperty(At,"__esModule",{value:!0});var Ct=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Rt=function(e){return e&&e.__esModule?e:{default:e}}(Ve),Bt=Xe;var It=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,Bt.C_START_CHAR+t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Rt.default),Ct(e,[{key:"valid",value:function(){return new RegExp("^"+Bt.C_CHARS+"+$").test(this.data)}}]),e}();At.default=It,Object.defineProperty(Ke,"__esModule",{value:!0}),Ke.CODE128C=Ke.CODE128B=Ke.CODE128A=Ke.CODE128=void 0;var Mt=Ut(Je),Lt=Ut(wt),$t=Ut(Ot),Dt=Ut(At);function Ut(e){return e&&e.__esModule?e:{default:e}}Ke.CODE128=Mt.default,Ke.CODE128A=Lt.default,Ke.CODE128B=$t.default,Ke.CODE128C=Dt.default;var Nt={},zt={},Ht={};Object.defineProperty(Ht,"__esModule",{value:!0}),Ht.SIDE_BIN="101",Ht.MIDDLE_BIN="01010",Ht.BINARIES={L:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],G:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"],R:["1110010","1100110","1101100","1000010","1011100","1001110","1010000","1000100","1001000","1110100"],O:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],E:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"]},Ht.EAN2_STRUCTURE=["LL","LG","GL","GG"],Ht.EAN5_STRUCTURE=["GGLLL","GLGLL","GLLGL","GLLLG","LGGLL","LLGGL","LLLGG","LGLGL","LGLLG","LLGLG"],Ht.EAN13_STRUCTURE=["LLLLLL","LLGLGG","LLGGLG","LLGGGL","LGLLGG","LGGLLG","LGGGLL","LGLGLG","LGLGGL","LGGLGL"];var Ft={},Gt={};Object.defineProperty(Gt,"__esModule",{value:!0});var qt=Ht;Gt.default=function(e,t,r){var n=e.split("").map(function(e,r){return qt.BINARIES[t[r]]}).map(function(t,r){return t?t[e[r]]:""});if(r){var i=e.length-1;n=n.map(function(e,t){return t<i?e+r:e})}return n.join("")},Object.defineProperty(Ft,"__esModule",{value:!0});var Wt=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Kt=Ht,Jt=Xt(Gt),Vt=Xt($e);function Xt(e){return e&&e.__esModule?e:{default:e}}var Yt=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r));return n.fontSize=!r.flat&&r.fontSize>10*r.width?10*r.width:r.fontSize,n.guardHeight=r.height+n.fontSize/2+r.textMargin,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Vt.default),Wt(e,[{key:"encode",value:function(){return this.options.flat?this.encodeFlat():this.encodeGuarded()}},{key:"leftText",value:function(e,t){return this.text.substr(e,t)}},{key:"leftEncode",value:function(e,t){return(0,Jt.default)(e,t)}},{key:"rightText",value:function(e,t){return this.text.substr(e,t)}},{key:"rightEncode",value:function(e,t){return(0,Jt.default)(e,t)}},{key:"encodeGuarded",value:function(){var e={fontSize:this.fontSize},t={height:this.guardHeight};return[{data:Kt.SIDE_BIN,options:t},{data:this.leftEncode(),text:this.leftText(),options:e},{data:Kt.MIDDLE_BIN,options:t},{data:this.rightEncode(),text:this.rightText(),options:e},{data:Kt.SIDE_BIN,options:t}]}},{key:"encodeFlat",value:function(){return{data:[Kt.SIDE_BIN,this.leftEncode(),Kt.MIDDLE_BIN,this.rightEncode(),Kt.SIDE_BIN].join(""),text:this.text}}}]),e}();Ft.default=Yt,Object.defineProperty(zt,"__esModule",{value:!0});var Qt=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Zt=function e(t,r,n){null===t&&(t=Function.prototype);var i=Object.getOwnPropertyDescriptor(t,r);if(void 0===i){var o=Object.getPrototypeOf(t);return null===o?void 0:e(o,r,n)}if("value"in i)return i.value;var s=i.get;return void 0!==s?s.call(n):void 0},er=Ht,tr=function(e){return e&&e.__esModule?e:{default:e}}(Ft);var rr=function(e){return(10-e.substr(0,12).split("").map(function(e){return+e}).reduce(function(e,t,r){return r%2?e+3*t:e+t},0)%10)%10},nr=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),-1!==t.search(/^[0-9]{12}$/)&&(t+=rr(t));var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r));return n.lastChar=r.lastChar,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,tr.default),Qt(e,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{13}$/)&&+this.data[12]===rr(this.data)}},{key:"leftText",value:function(){return Zt(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"leftText",this).call(this,1,6)}},{key:"leftEncode",value:function(){var t=this.data.substr(1,6),r=er.EAN13_STRUCTURE[this.data[0]];return Zt(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"leftEncode",this).call(this,t,r)}},{key:"rightText",value:function(){return Zt(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"rightText",this).call(this,7,6)}},{key:"rightEncode",value:function(){var t=this.data.substr(7,6);return Zt(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"rightEncode",this).call(this,t,"RRRRRR")}},{key:"encodeGuarded",value:function(){var t=Zt(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"encodeGuarded",this).call(this);return this.options.displayValue&&(t.unshift({data:"000000000000",text:this.text.substr(0,1),options:{textAlign:"left",fontSize:this.fontSize}}),this.options.lastChar&&(t.push({data:"00"}),t.push({data:"00000",text:this.options.lastChar,options:{fontSize:this.fontSize}}))),t}}]),e}();zt.default=nr;var ir={};Object.defineProperty(ir,"__esModule",{value:!0});var or=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),sr=function e(t,r,n){null===t&&(t=Function.prototype);var i=Object.getOwnPropertyDescriptor(t,r);if(void 0===i){var o=Object.getPrototypeOf(t);return null===o?void 0:e(o,r,n)}if("value"in i)return i.value;var s=i.get;return void 0!==s?s.call(n):void 0},ar=function(e){return e&&e.__esModule?e:{default:e}}(Ft);var cr=function(e){return(10-e.substr(0,7).split("").map(function(e){return+e}).reduce(function(e,t,r){return r%2?e+t:e+3*t},0)%10)%10},ur=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),-1!==t.search(/^[0-9]{7}$/)&&(t+=cr(t)),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,ar.default),or(e,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{8}$/)&&+this.data[7]===cr(this.data)}},{key:"leftText",value:function(){return sr(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"leftText",this).call(this,0,4)}},{key:"leftEncode",value:function(){var t=this.data.substr(0,4);return sr(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"leftEncode",this).call(this,t,"LLLL")}},{key:"rightText",value:function(){return sr(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"rightText",this).call(this,4,4)}},{key:"rightEncode",value:function(){var t=this.data.substr(4,4);return sr(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"rightEncode",this).call(this,t,"RRRR")}}]),e}();ir.default=ur;var lr={};Object.defineProperty(lr,"__esModule",{value:!0});var hr=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),dr=Ht,fr=gr(Gt),pr=gr($e);function gr(e){return e&&e.__esModule?e:{default:e}}var vr=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,pr.default),hr(e,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{5}$/)}},{key:"encode",value:function(){var e,t=dr.EAN5_STRUCTURE[(e=this.data,e.split("").map(function(e){return+e}).reduce(function(e,t,r){return r%2?e+9*t:e+3*t},0)%10)];return{data:"1011"+(0,fr.default)(this.data,t,"01"),text:this.text}}}]),e}();lr.default=vr;var yr={};Object.defineProperty(yr,"__esModule",{value:!0});var _r=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),br=Ht,wr=kr(Gt),mr=kr($e);function kr(e){return e&&e.__esModule?e:{default:e}}var Er=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,mr.default),_r(e,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{2}$/)}},{key:"encode",value:function(){var e=br.EAN2_STRUCTURE[parseInt(this.data)%4];return{data:"1011"+(0,wr.default)(this.data,e,"01"),text:this.text}}}]),e}();yr.default=Er;var Sr={};Object.defineProperty(Sr,"__esModule",{value:!0});var Or=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();Sr.checksum=Ar;var xr=Pr(Gt),Tr=Pr($e);function Pr(e){return e&&e.__esModule?e:{default:e}}var jr=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),-1!==t.search(/^[0-9]{11}$/)&&(t+=Ar(t));var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r));return n.displayValue=r.displayValue,r.fontSize>10*r.width?n.fontSize=10*r.width:n.fontSize=r.fontSize,n.guardHeight=r.height+n.fontSize/2+r.textMargin,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Tr.default),Or(e,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{12}$/)&&this.data[11]==Ar(this.data)}},{key:"encode",value:function(){return this.options.flat?this.flatEncoding():this.guardedEncoding()}},{key:"flatEncoding",value:function(){var e="";return e+="101",e+=(0,xr.default)(this.data.substr(0,6),"LLLLLL"),e+="01010",e+=(0,xr.default)(this.data.substr(6,6),"RRRRRR"),{data:e+="101",text:this.text}}},{key:"guardedEncoding",value:function(){var e=[];return this.displayValue&&e.push({data:"00000000",text:this.text.substr(0,1),options:{textAlign:"left",fontSize:this.fontSize}}),e.push({data:"101"+(0,xr.default)(this.data[0],"L"),options:{height:this.guardHeight}}),e.push({data:(0,xr.default)(this.data.substr(1,5),"LLLLL"),text:this.text.substr(1,5),options:{fontSize:this.fontSize}}),e.push({data:"01010",options:{height:this.guardHeight}}),e.push({data:(0,xr.default)(this.data.substr(6,5),"RRRRR"),text:this.text.substr(6,5),options:{fontSize:this.fontSize}}),e.push({data:(0,xr.default)(this.data[11],"R")+"101",options:{height:this.guardHeight}}),this.displayValue&&e.push({data:"00000000",text:this.text.substr(11,1),options:{textAlign:"right",fontSize:this.fontSize}}),e}}]),e}();function Ar(e){var t,r=0;for(t=1;t<11;t+=2)r+=parseInt(e[t]);for(t=0;t<11;t+=2)r+=3*parseInt(e[t]);return(10-r%10)%10}Sr.default=jr;var Cr={};Object.defineProperty(Cr,"__esModule",{value:!0});var Rr=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Br=Lr(Gt),Ir=Lr($e),Mr=Sr;function Lr(e){return e&&e.__esModule?e:{default:e}}function $r(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var Dr=["XX00000XXX","XX10000XXX","XX20000XXX","XXX00000XX","XXXX00000X","XXXXX00005","XXXXX00006","XXXXX00007","XXXXX00008","XXXXX00009"],Ur=[["EEEOOO","OOOEEE"],["EEOEOO","OOEOEE"],["EEOOEO","OOEEOE"],["EEOOOE","OOEEEO"],["EOEEOO","OEOOEE"],["EOOEEO","OEEOOE"],["EOOOEE","OEEEOO"],["EOEOEO","OEOEOE"],["EOEOOE","OEOEEO"],["EOOEOE","OEEOEO"]],Nr=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=$r(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r));if(n.isValid=!1,-1!==t.search(/^[0-9]{6}$/))n.middleDigits=t,n.upcA=zr(t,"0"),n.text=r.text||""+n.upcA[0]+t+n.upcA[n.upcA.length-1],n.isValid=!0;else{if(-1===t.search(/^[01][0-9]{7}$/))return $r(n);if(n.middleDigits=t.substring(1,t.length-1),n.upcA=zr(n.middleDigits,t[0]),n.upcA[n.upcA.length-1]!==t[t.length-1])return $r(n);n.isValid=!0}return n.displayValue=r.displayValue,r.fontSize>10*r.width?n.fontSize=10*r.width:n.fontSize=r.fontSize,n.guardHeight=r.height+n.fontSize/2+r.textMargin,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Ir.default),Rr(e,[{key:"valid",value:function(){return this.isValid}},{key:"encode",value:function(){return this.options.flat?this.flatEncoding():this.guardedEncoding()}},{key:"flatEncoding",value:function(){var e="";return e+="101",e+=this.encodeMiddleDigits(),{data:e+="010101",text:this.text}}},{key:"guardedEncoding",value:function(){var e=[];return this.displayValue&&e.push({data:"00000000",text:this.text[0],options:{textAlign:"left",fontSize:this.fontSize}}),e.push({data:"101",options:{height:this.guardHeight}}),e.push({data:this.encodeMiddleDigits(),text:this.text.substring(1,7),options:{fontSize:this.fontSize}}),e.push({data:"010101",options:{height:this.guardHeight}}),this.displayValue&&e.push({data:"00000000",text:this.text[7],options:{textAlign:"right",fontSize:this.fontSize}}),e}},{key:"encodeMiddleDigits",value:function(){var e=this.upcA[0],t=this.upcA[this.upcA.length-1],r=Ur[parseInt(t)][parseInt(e)];return(0,Br.default)(this.middleDigits,r)}}]),e}();function zr(e,t){for(var r=parseInt(e[e.length-1]),n=Dr[r],i="",o=0,s=0;s<n.length;s++){var a=n[s];i+="X"===a?e[o++]:a}return""+(i=""+t+i)+(0,Mr.checksum)(i)}Cr.default=Nr,Object.defineProperty(Nt,"__esModule",{value:!0}),Nt.UPCE=Nt.UPC=Nt.EAN2=Nt.EAN5=Nt.EAN8=Nt.EAN13=void 0;var Hr=Jr(zt),Fr=Jr(ir),Gr=Jr(lr),qr=Jr(yr),Wr=Jr(Sr),Kr=Jr(Cr);function Jr(e){return e&&e.__esModule?e:{default:e}}Nt.EAN13=Hr.default,Nt.EAN8=Fr.default,Nt.EAN5=Gr.default,Nt.EAN2=qr.default,Nt.UPC=Wr.default,Nt.UPCE=Kr.default;var Vr={},Xr={},Yr={};Object.defineProperty(Yr,"__esModule",{value:!0}),Yr.START_BIN="1010",Yr.END_BIN="11101",Yr.BINARIES=["00110","10001","01001","11000","00101","10100","01100","00011","10010","01010"],Object.defineProperty(Xr,"__esModule",{value:!0});var Qr=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Zr=Yr,en=function(e){return e&&e.__esModule?e:{default:e}}($e);var tn=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,en.default),Qr(e,[{key:"valid",value:function(){return-1!==this.data.search(/^([0-9]{2})+$/)}},{key:"encode",value:function(){var e=this,t=this.data.match(/.{2}/g).map(function(t){return e.encodePair(t)}).join("");return{data:Zr.START_BIN+t+Zr.END_BIN,text:this.text}}},{key:"encodePair",value:function(e){var t=Zr.BINARIES[e[1]];return Zr.BINARIES[e[0]].split("").map(function(e,r){return("1"===e?"111":"1")+("1"===t[r]?"000":"0")}).join("")}}]),e}();Xr.default=tn;var rn={};Object.defineProperty(rn,"__esModule",{value:!0});var nn=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),on=function(e){return e&&e.__esModule?e:{default:e}}(Xr);var sn=function(e){var t=e.substr(0,13).split("").map(function(e){return parseInt(e,10)}).reduce(function(e,t,r){return e+t*(3-r%2*2)},0);return 10*Math.ceil(t/10)-t},an=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),-1!==t.search(/^[0-9]{13}$/)&&(t+=sn(t)),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,on.default),nn(e,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{14}$/)&&+this.data[13]===sn(this.data)}}]),e}();rn.default=an,Object.defineProperty(Vr,"__esModule",{value:!0}),Vr.ITF14=Vr.ITF=void 0;var cn=ln(Xr),un=ln(rn);function ln(e){return e&&e.__esModule?e:{default:e}}Vr.ITF=cn.default,Vr.ITF14=un.default;var hn={},dn={};Object.defineProperty(dn,"__esModule",{value:!0});var fn=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),pn=function(e){return e&&e.__esModule?e:{default:e}}($e);var gn=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,pn.default),fn(e,[{key:"encode",value:function(){for(var e="110",t=0;t<this.data.length;t++){var r=parseInt(this.data[t]).toString(2);r=vn(r,4-r.length);for(var n=0;n<r.length;n++)e+="0"==r[n]?"100":"110"}return{data:e+="1001",text:this.text}}},{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]+$/)}}]),e}();function vn(e,t){for(var r=0;r<t;r++)e="0"+e;return e}dn.default=gn;var yn={},_n={};Object.defineProperty(_n,"__esModule",{value:!0}),_n.mod10=function(e){for(var t=0,r=0;r<e.length;r++){var n=parseInt(e[r]);(r+e.length)%2==0?t+=n:t+=2*n%10+Math.floor(2*n/10)}return(10-t%10)%10},_n.mod11=function(e){for(var t=0,r=[2,3,4,5,6,7],n=0;n<e.length;n++){var i=parseInt(e[e.length-1-n]);t+=r[n%r.length]*i}return(11-t%11)%11},Object.defineProperty(yn,"__esModule",{value:!0});var bn=function(e){return e&&e.__esModule?e:{default:e}}(dn),wn=_n;var mn=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t+(0,wn.mod10)(t),r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,bn.default),e}();yn.default=mn;var kn={};Object.defineProperty(kn,"__esModule",{value:!0});var En=function(e){return e&&e.__esModule?e:{default:e}}(dn),Sn=_n;var On=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t+(0,Sn.mod11)(t),r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,En.default),e}();kn.default=On;var xn={};Object.defineProperty(xn,"__esModule",{value:!0});var Tn=function(e){return e&&e.__esModule?e:{default:e}}(dn),Pn=_n;var jn=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t+=(0,Pn.mod10)(t),t+=(0,Pn.mod10)(t),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Tn.default),e}();xn.default=jn;var An={};Object.defineProperty(An,"__esModule",{value:!0});var Cn=function(e){return e&&e.__esModule?e:{default:e}}(dn),Rn=_n;var Bn=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t+=(0,Rn.mod11)(t),t+=(0,Rn.mod10)(t),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Cn.default),e}();An.default=Bn,Object.defineProperty(hn,"__esModule",{value:!0}),hn.MSI1110=hn.MSI1010=hn.MSI11=hn.MSI10=hn.MSI=void 0;var In=Un(dn),Mn=Un(yn),Ln=Un(kn),$n=Un(xn),Dn=Un(An);function Un(e){return e&&e.__esModule?e:{default:e}}hn.MSI=In.default,hn.MSI10=Mn.default,hn.MSI11=Ln.default,hn.MSI1010=$n.default,hn.MSI1110=Dn.default;var Nn={};Object.defineProperty(Nn,"__esModule",{value:!0}),Nn.pharmacode=void 0;var zn=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Hn=function(e){return e&&e.__esModule?e:{default:e}}($e);var Fn=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r));return n.number=parseInt(t,10),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Hn.default),zn(e,[{key:"encode",value:function(){for(var e=this.number,t="";!isNaN(e)&&0!=e;)e%2==0?(t="11100"+t,e=(e-2)/2):(t="100"+t,e=(e-1)/2);return{data:t=t.slice(0,-2),text:this.text}}},{key:"valid",value:function(){return this.number>=3&&this.number<=131070}}]),e}();Nn.pharmacode=Fn;var Gn={};Object.defineProperty(Gn,"__esModule",{value:!0}),Gn.codabar=void 0;var qn=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Wn=function(e){return e&&e.__esModule?e:{default:e}}($e);var Kn=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),0===t.search(/^[0-9\-\$\:\.\+\/]+$/)&&(t="A"+t+"A");var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t.toUpperCase(),r));return n.text=n.options.text||n.text.replace(/[A-D]/g,""),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Wn.default),qn(e,[{key:"valid",value:function(){return-1!==this.data.search(/^[A-D][0-9\-\$\:\.\+\/]+[A-D]$/)}},{key:"encode",value:function(){for(var e=[],t=this.getEncodings(),r=0;r<this.data.length;r++)e.push(t[this.data.charAt(r)]),r!==this.data.length-1&&e.push("0");return{text:this.text,data:e.join("")}}},{key:"getEncodings",value:function(){return{0:"101010011",1:"101011001",2:"101001011",3:"110010101",4:"101101001",5:"110101001",6:"100101011",7:"100101101",8:"100110101",9:"110100101","-":"101001101",$:"101100101",":":"1101011011","/":"1101101011",".":"1101101101","+":"1011011011",A:"1011001001",B:"1001001011",C:"1010010011",D:"1010011001"}}}]),e}();Gn.codabar=Kn;var Jn={},Vn={},Xn={};Object.defineProperty(Xn,"__esModule",{value:!0}),Xn.SYMBOLS=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","-","."," ","$","/","+","%","($)","(%)","(/)","(+)","ÿ"],Xn.BINARIES=["100010100","101001000","101000100","101000010","100101000","100100100","100100010","101010000","100010010","100001010","110101000","110100100","110100010","110010100","110010010","110001010","101101000","101100100","101100010","100110100","100011010","101011000","101001100","101000110","100101100","100010110","110110100","110110010","110101100","110100110","110010110","110011010","101101100","101100110","100110110","100111010","100101110","111010100","111010010","111001010","101101110","101110110","110101110","100100110","111011010","111010110","100110010","101011110"],Xn.MULTI_SYMBOLS={"\0":["(%)","U"],"":["($)","A"],"":["($)","B"],"":["($)","C"],"":["($)","D"],"":["($)","E"],"":["($)","F"],"":["($)","G"],"\b":["($)","H"],"\t":["($)","I"],"\n":["($)","J"],"\v":["($)","K"],"\f":["($)","L"],"\r":["($)","M"],"":["($)","N"],"":["($)","O"],"":["($)","P"],"":["($)","Q"],"":["($)","R"],"":["($)","S"],"":["($)","T"],"":["($)","U"],"":["($)","V"],"":["($)","W"],"":["($)","X"],"":["($)","Y"],"":["($)","Z"],"":["(%)","A"],"":["(%)","B"],"":["(%)","C"],"":["(%)","D"],"":["(%)","E"],"!":["(/)","A"],'"':["(/)","B"],"#":["(/)","C"],"&":["(/)","F"],"'":["(/)","G"],"(":["(/)","H"],")":["(/)","I"],"*":["(/)","J"],",":["(/)","L"],":":["(/)","Z"],";":["(%)","F"],"<":["(%)","G"],"=":["(%)","H"],">":["(%)","I"],"?":["(%)","J"],"@":["(%)","V"],"[":["(%)","K"],"\\":["(%)","L"],"]":["(%)","M"],"^":["(%)","N"],_:["(%)","O"],"`":["(%)","W"],a:["(+)","A"],b:["(+)","B"],c:["(+)","C"],d:["(+)","D"],e:["(+)","E"],f:["(+)","F"],g:["(+)","G"],h:["(+)","H"],i:["(+)","I"],j:["(+)","J"],k:["(+)","K"],l:["(+)","L"],m:["(+)","M"],n:["(+)","N"],o:["(+)","O"],p:["(+)","P"],q:["(+)","Q"],r:["(+)","R"],s:["(+)","S"],t:["(+)","T"],u:["(+)","U"],v:["(+)","V"],w:["(+)","W"],x:["(+)","X"],y:["(+)","Y"],z:["(+)","Z"],"{":["(%)","P"],"|":["(%)","Q"],"}":["(%)","R"],"~":["(%)","S"],"":["(%)","T"]},Object.defineProperty(Vn,"__esModule",{value:!0});var Yn=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Qn=Xn,Zn=function(e){return e&&e.__esModule?e:{default:e}}($e);var ei=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Zn.default),Yn(e,[{key:"valid",value:function(){return/^[0-9A-Z\-. $/+%]+$/.test(this.data)}},{key:"encode",value:function(){var t=this.data.split("").flatMap(function(e){return Qn.MULTI_SYMBOLS[e]||e}),r=t.map(function(t){return e.getEncoding(t)}).join(""),n=e.checksum(t,20),i=e.checksum(t.concat(n),15);return{text:this.text,data:e.getEncoding("ÿ")+r+e.getEncoding(n)+e.getEncoding(i)+e.getEncoding("ÿ")+"1"}}}],[{key:"getEncoding",value:function(t){return Qn.BINARIES[e.symbolValue(t)]}},{key:"getSymbol",value:function(e){return Qn.SYMBOLS[e]}},{key:"symbolValue",value:function(e){return Qn.SYMBOLS.indexOf(e)}},{key:"checksum",value:function(t,r){var n=t.slice().reverse().reduce(function(t,n,i){var o=i%r+1;return t+e.symbolValue(n)*o},0);return e.getSymbol(n%47)}}]),e}();Vn.default=ei;var ti={};Object.defineProperty(ti,"__esModule",{value:!0});var ri=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),ni=function(e){return e&&e.__esModule?e:{default:e}}(Vn);var ii=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,ni.default),ri(e,[{key:"valid",value:function(){return/^[\x00-\x7f]+$/.test(this.data)}}]),e}();ti.default=ii,Object.defineProperty(Jn,"__esModule",{value:!0}),Jn.CODE93FullASCII=Jn.CODE93=void 0;var oi=ai(Vn),si=ai(ti);function ai(e){return e&&e.__esModule?e:{default:e}}Jn.CODE93=oi.default,Jn.CODE93FullASCII=si.default;var ci={};Object.defineProperty(ci,"__esModule",{value:!0}),ci.GenericBarcode=void 0;var ui=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),li=function(e){return e&&e.__esModule?e:{default:e}}($e);var hi=function(){function e(t,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,li.default),ui(e,[{key:"encode",value:function(){return{data:"10101010101010101010101010101010101010101",text:this.text}}},{key:"valid",value:function(){return!0}}]),e}();ci.GenericBarcode=hi,Object.defineProperty(Me,"__esModule",{value:!0});var di=Le,fi=Ke,pi=Nt,gi=Vr,vi=hn,yi=Nn,_i=Gn,bi=Jn,wi=ci;Me.default={CODE39:di.CODE39,CODE128:fi.CODE128,CODE128A:fi.CODE128A,CODE128B:fi.CODE128B,CODE128C:fi.CODE128C,EAN13:pi.EAN13,EAN8:pi.EAN8,EAN5:pi.EAN5,EAN2:pi.EAN2,UPC:pi.UPC,UPCE:pi.UPCE,ITF14:gi.ITF14,ITF:gi.ITF,MSI:vi.MSI,MSI10:vi.MSI10,MSI11:vi.MSI11,MSI1010:vi.MSI1010,MSI1110:vi.MSI1110,pharmacode:yi.pharmacode,codabar:_i.codabar,CODE93:bi.CODE93,CODE93FullASCII:bi.CODE93FullASCII,GenericBarcode:wi.GenericBarcode};var mi={};Object.defineProperty(mi,"__esModule",{value:!0});var ki=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e};mi.default=function(e,t){return ki({},e,t)};var Ei={};Object.defineProperty(Ei,"__esModule",{value:!0}),Ei.default=function(e){var t=[];return function e(r){if(Array.isArray(r))for(var n=0;n<r.length;n++)e(r[n]);else r.text=r.text||"",r.data=r.data||"",t.push(r)}(e),t};var Si={};Object.defineProperty(Si,"__esModule",{value:!0}),Si.default=function(e){return e.marginTop=e.marginTop||e.margin,e.marginBottom=e.marginBottom||e.margin,e.marginRight=e.marginRight||e.margin,e.marginLeft=e.marginLeft||e.margin,e};var Oi={},xi={},Ti={};Object.defineProperty(Ti,"__esModule",{value:!0}),Ti.default=function(e){var t=["width","height","textMargin","fontSize","margin","marginTop","marginBottom","marginLeft","marginRight"];for(var r in t)t.hasOwnProperty(r)&&"string"==typeof e[r=t[r]]&&(e[r]=parseInt(e[r],10));"string"==typeof e.displayValue&&(e.displayValue="false"!=e.displayValue);return e};var Pi={};Object.defineProperty(Pi,"__esModule",{value:!0});var ji={width:2,height:100,format:"auto",displayValue:!0,fontOptions:"",font:"monospace",text:void 0,textAlign:"center",textPosition:"bottom",textMargin:2,fontSize:20,background:"#ffffff",lineColor:"#000000",margin:10,marginTop:void 0,marginBottom:void 0,marginLeft:void 0,marginRight:void 0,valid:function(){}};Pi.default=ji,Object.defineProperty(xi,"__esModule",{value:!0});var Ai=Ri(Ti),Ci=Ri(Pi);function Ri(e){return e&&e.__esModule?e:{default:e}}xi.default=function(e){var t={};for(var r in Ci.default)Ci.default.hasOwnProperty(r)&&(e.hasAttribute("jsbarcode-"+r.toLowerCase())&&(t[r]=e.getAttribute("jsbarcode-"+r.toLowerCase())),e.hasAttribute("data-"+r.toLowerCase())&&(t[r]=e.getAttribute("data-"+r.toLowerCase())));return t.value=e.getAttribute("jsbarcode-value")||e.getAttribute("data-value"),t=(0,Ai.default)(t)};var Bi={},Ii={},Mi={};Object.defineProperty(Mi,"__esModule",{value:!0}),Mi.getTotalWidthOfEncodings=Mi.calculateEncodingAttributes=Mi.getBarcodePadding=Mi.getEncodingHeight=Mi.getMaximumHeightOfEncodings=void 0;var Li=function(e){return e&&e.__esModule?e:{default:e}}(mi);function $i(e,t){return t.height+(t.displayValue&&e.text.length>0?t.fontSize+t.textMargin:0)+t.marginTop+t.marginBottom}function Di(e,t,r){if(r.displayValue&&t<e){if("center"==r.textAlign)return Math.floor((e-t)/2);if("left"==r.textAlign)return 0;if("right"==r.textAlign)return Math.floor(e-t)}return 0}function Ui(e,t,r){var n;if(r)n=r;else{if("undefined"==typeof document)return 0;n=document.createElement("canvas").getContext("2d")}n.font=t.fontOptions+" "+t.fontSize+"px "+t.font;var i=n.measureText(e);return i?i.width:0}Mi.getMaximumHeightOfEncodings=function(e){for(var t=0,r=0;r<e.length;r++)e[r].height>t&&(t=e[r].height);return t},Mi.getEncodingHeight=$i,Mi.getBarcodePadding=Di,Mi.calculateEncodingAttributes=function(e,t,r){for(var n=0;n<e.length;n++){var i,o=e[n],s=(0,Li.default)(t,o.options);i=s.displayValue?Ui(o.text,s,r):0;var a=o.data.length*s.width;o.width=Math.ceil(Math.max(i,a)),o.height=$i(o,s),o.barcodePadding=Di(i,a,s)}},Mi.getTotalWidthOfEncodings=function(e){for(var t=0,r=0;r<e.length;r++)t+=e[r].width;return t},Object.defineProperty(Ii,"__esModule",{value:!0});var Ni=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),zi=function(e){return e&&e.__esModule?e:{default:e}}(mi),Hi=Mi;var Fi=function(){function e(t,r,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.canvas=t,this.encodings=r,this.options=n}return Ni(e,[{key:"render",value:function(){if(!this.canvas.getContext)throw new Error("The browser does not support canvas.");this.prepareCanvas();for(var e=0;e<this.encodings.length;e++){var t=(0,zi.default)(this.options,this.encodings[e].options);this.drawCanvasBarcode(t,this.encodings[e]),this.drawCanvasText(t,this.encodings[e]),this.moveCanvasDrawing(this.encodings[e])}this.restoreCanvas()}},{key:"prepareCanvas",value:function(){var e=this.canvas.getContext("2d");e.save(),(0,Hi.calculateEncodingAttributes)(this.encodings,this.options,e);var t=(0,Hi.getTotalWidthOfEncodings)(this.encodings),r=(0,Hi.getMaximumHeightOfEncodings)(this.encodings);this.canvas.width=t+this.options.marginLeft+this.options.marginRight,this.canvas.height=r,e.clearRect(0,0,this.canvas.width,this.canvas.height),this.options.background&&(e.fillStyle=this.options.background,e.fillRect(0,0,this.canvas.width,this.canvas.height)),e.translate(this.options.marginLeft,0)}},{key:"drawCanvasBarcode",value:function(e,t){var r,n=this.canvas.getContext("2d"),i=t.data;r="top"==e.textPosition?e.marginTop+e.fontSize+e.textMargin:e.marginTop,n.fillStyle=e.lineColor;for(var o=0;o<i.length;o++){var s=o*e.width+t.barcodePadding;"1"===i[o]?n.fillRect(s,r,e.width,e.height):i[o]&&n.fillRect(s,r,e.width,e.height*i[o])}}},{key:"drawCanvasText",value:function(e,t){var r,n,i=this.canvas.getContext("2d"),o=e.fontOptions+" "+e.fontSize+"px "+e.font;e.displayValue&&(n="top"==e.textPosition?e.marginTop+e.fontSize-e.textMargin:e.height+e.textMargin+e.marginTop+e.fontSize,i.font=o,"left"==e.textAlign||t.barcodePadding>0?(r=0,i.textAlign="left"):"right"==e.textAlign?(r=t.width-1,i.textAlign="right"):(r=t.width/2,i.textAlign="center"),i.fillText(t.text,r,n))}},{key:"moveCanvasDrawing",value:function(e){this.canvas.getContext("2d").translate(e.width,0)}},{key:"restoreCanvas",value:function(){this.canvas.getContext("2d").restore()}}]),e}();Ii.default=Fi;var Gi={};Object.defineProperty(Gi,"__esModule",{value:!0});var qi=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Wi=function(e){return e&&e.__esModule?e:{default:e}}(mi),Ki=Mi;var Ji="http://www.w3.org/2000/svg",Vi=function(){function e(t,r,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.svg=t,this.encodings=r,this.options=n,this.document=n.xmlDocument||document}return qi(e,[{key:"render",value:function(){var e=this.options.marginLeft;this.prepareSVG();for(var t=0;t<this.encodings.length;t++){var r=this.encodings[t],n=(0,Wi.default)(this.options,r.options),i=this.createGroup(e,n.marginTop,this.svg);this.setGroupOptions(i,n),this.drawSvgBarcode(i,n,r),this.drawSVGText(i,n,r),e+=r.width}}},{key:"prepareSVG",value:function(){for(;this.svg.firstChild;)this.svg.removeChild(this.svg.firstChild);(0,Ki.calculateEncodingAttributes)(this.encodings,this.options);var e=(0,Ki.getTotalWidthOfEncodings)(this.encodings),t=(0,Ki.getMaximumHeightOfEncodings)(this.encodings),r=e+this.options.marginLeft+this.options.marginRight;this.setSvgAttributes(r,t),this.options.background&&this.drawRect(0,0,r,t,this.svg).setAttribute("style","fill:"+this.options.background+";")}},{key:"drawSvgBarcode",value:function(e,t,r){var n,i=r.data;n="top"==t.textPosition?t.fontSize+t.textMargin:0;for(var o=0,s=0,a=0;a<i.length;a++)s=a*t.width+r.barcodePadding,"1"===i[a]?o++:o>0&&(this.drawRect(s-t.width*o,n,t.width*o,t.height,e),o=0);o>0&&this.drawRect(s-t.width*(o-1),n,t.width*o,t.height,e)}},{key:"drawSVGText",value:function(e,t,r){var n,i,o=this.document.createElementNS(Ji,"text");t.displayValue&&(o.setAttribute("style","font:"+t.fontOptions+" "+t.fontSize+"px "+t.font),i="top"==t.textPosition?t.fontSize-t.textMargin:t.height+t.textMargin+t.fontSize,"left"==t.textAlign||r.barcodePadding>0?(n=0,o.setAttribute("text-anchor","start")):"right"==t.textAlign?(n=r.width-1,o.setAttribute("text-anchor","end")):(n=r.width/2,o.setAttribute("text-anchor","middle")),o.setAttribute("x",n),o.setAttribute("y",i),o.appendChild(this.document.createTextNode(r.text)),e.appendChild(o))}},{key:"setSvgAttributes",value:function(e,t){var r=this.svg;r.setAttribute("width",e+"px"),r.setAttribute("height",t+"px"),r.setAttribute("x","0px"),r.setAttribute("y","0px"),r.setAttribute("viewBox","0 0 "+e+" "+t),r.setAttribute("xmlns",Ji),r.setAttribute("version","1.1"),r.setAttribute("style","transform: translate(0,0)")}},{key:"createGroup",value:function(e,t,r){var n=this.document.createElementNS(Ji,"g");return n.setAttribute("transform","translate("+e+", "+t+")"),r.appendChild(n),n}},{key:"setGroupOptions",value:function(e,t){e.setAttribute("style","fill:"+t.lineColor+";")}},{key:"drawRect",value:function(e,t,r,n,i){var o=this.document.createElementNS(Ji,"rect");return o.setAttribute("x",e),o.setAttribute("y",t),o.setAttribute("width",r),o.setAttribute("height",n),i.appendChild(o),o}}]),e}();Gi.default=Vi;var Xi={};Object.defineProperty(Xi,"__esModule",{value:!0});var Yi=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();var Qi=function(){function e(t,r,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.object=t,this.encodings=r,this.options=n}return Yi(e,[{key:"render",value:function(){this.object.encodings=this.encodings}}]),e}();Xi.default=Qi,Object.defineProperty(Bi,"__esModule",{value:!0});var Zi=ro(Ii),eo=ro(Gi),to=ro(Xi);function ro(e){return e&&e.__esModule?e:{default:e}}Bi.default={CanvasRenderer:Zi.default,SVGRenderer:eo.default,ObjectRenderer:to.default};var no={};function io(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function oo(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function so(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(no,"__esModule",{value:!0});var ao=function(){function e(t,r){io(this,e);var n=oo(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return n.name="InvalidInputException",n.symbology=t,n.input=r,n.message='"'+n.input+'" is not a valid input for '+n.symbology,n}return so(e,Error),e}(),co=function(){function e(){io(this,e);var t=oo(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return t.name="InvalidElementException",t.message="Not supported type to render on",t}return so(e,Error),e}(),uo=function(){function e(){io(this,e);var t=oo(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return t.name="NoElementException",t.message="No element to render on.",t}return so(e,Error),e}();no.InvalidInputException=ao,no.InvalidElementException=co,no.NoElementException=uo,Object.defineProperty(Oi,"__esModule",{value:!0});var lo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ho=go(xi),fo=go(Bi),po=no;function go(e){return e&&e.__esModule?e:{default:e}}function vo(e){if("string"==typeof e)return function(e){var t=document.querySelectorAll(e);if(0===t.length)return;for(var r=[],n=0;n<t.length;n++)r.push(vo(t[n]));return r}(e);if(Array.isArray(e)){for(var t=[],r=0;r<e.length;r++)t.push(vo(e[r]));return t}if("undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLImageElement)return function(e){var t=document.createElement("canvas");return{element:t,options:(0,ho.default)(e),renderer:fo.default.CanvasRenderer,afterRender:function(){e.setAttribute("src",t.toDataURL())}}}(e);if(e&&e.nodeName&&"svg"===e.nodeName.toLowerCase()||"undefined"!=typeof SVGElement&&e instanceof SVGElement)return{element:e,options:(0,ho.default)(e),renderer:fo.default.SVGRenderer};if("undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement)return{element:e,options:(0,ho.default)(e),renderer:fo.default.CanvasRenderer};if(e&&e.getContext)return{element:e,renderer:fo.default.CanvasRenderer};if(e&&"object"===(void 0===e?"undefined":lo(e))&&!e.nodeName)return{element:e,renderer:fo.default.ObjectRenderer};throw new po.InvalidElementException}Oi.default=vo;var yo={};Object.defineProperty(yo,"__esModule",{value:!0});var _o=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();var bo=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.api=t}return _o(e,[{key:"handleCatch",value:function(e){if("InvalidInputException"!==e.name)throw e;if(this.api._options.valid===this.api._defaults.valid)throw e.message;this.api._options.valid(!1),this.api.render=function(){}}},{key:"wrapBarcodeCall",value:function(e){try{var t=e.apply(void 0,arguments);return this.api._options.valid(!0),t}catch(r){return this.handleCatch(r),this.api}}}]),e}();yo.default=bo;var wo=jo(Me),mo=jo(mi),ko=jo(Ei),Eo=jo(Si),So=jo(Oi),Oo=jo(Ti),xo=jo(yo),To=no,Po=jo(Pi);function jo(e){return e&&e.__esModule?e:{default:e}}var Ao=function(){},Co=function(e,t,r){var n=new Ao;if(void 0===e)throw Error("No element to render on was provided.");return n._renderProperties=(0,So.default)(e),n._encodings=[],n._options=Po.default,n._errorHandler=new xo.default(n),void 0!==t&&((r=r||{}).format||(r.format=Mo()),n.options(r)[r.format](t,r).render()),n};for(var Ro in Co.getModule=function(e){return wo.default[e]},wo.default)wo.default.hasOwnProperty(Ro)&&Bo(wo.default,Ro);function Bo(e,t){Ao.prototype[t]=Ao.prototype[t.toUpperCase()]=Ao.prototype[t.toLowerCase()]=function(r,n){var i=this;return i._errorHandler.wrapBarcodeCall(function(){n.text=void 0===n.text?void 0:""+n.text;var o=(0,mo.default)(i._options,n);o=(0,Oo.default)(o);var s=e[t],a=Io(r,s,o);return i._encodings.push(a),i})}}function Io(e,t,r){var n=new t(e=""+e,r);if(!n.valid())throw new To.InvalidInputException(n.constructor.name,e);var i=n.encode();i=(0,ko.default)(i);for(var o=0;o<i.length;o++)i[o].options=(0,mo.default)(r,i[o].options);return i}function Mo(){return wo.default.CODE128?"CODE128":Object.keys(wo.default)[0]}function Lo(e,t,r){t=(0,ko.default)(t);for(var n=0;n<t.length;n++)t[n].options=(0,mo.default)(r,t[n].options),(0,Eo.default)(t[n].options);(0,Eo.default)(r),new(0,e.renderer)(e.element,t,r).render(),e.afterRender&&e.afterRender()}Ao.prototype.options=function(e){return this._options=(0,mo.default)(this._options,e),this},Ao.prototype.blank=function(e){var t=new Array(e+1).join("0");return this._encodings.push({data:t}),this},Ao.prototype.init=function(){var e;if(this._renderProperties)for(var t in Array.isArray(this._renderProperties)||(this._renderProperties=[this._renderProperties]),this._renderProperties){e=this._renderProperties[t];var r=(0,mo.default)(this._options,e.options);"auto"==r.format&&(r.format=Mo()),this._errorHandler.wrapBarcodeCall(function(){var t=Io(r.value,wo.default[r.format.toUpperCase()],r);Lo(e,t,r)})}},Ao.prototype.render=function(){if(!this._renderProperties)throw new To.NoElementException;if(Array.isArray(this._renderProperties))for(var e=0;e<this._renderProperties.length;e++)Lo(this._renderProperties[e],this._encodings,this._options);else Lo(this._renderProperties,this._encodings,this._options);return this},Ao.prototype._defaults=Po.default,"undefined"!=typeof window&&(window.JsBarcode=Co),"undefined"!=typeof jQuery&&(jQuery.fn.JsBarcode=function(e,t){var r=[];return jQuery(this).each(function(){r.push(this)}),Co(r,e,t)});const $o=e(Co),Do={},Uo=function(e,t,r){if(!t||0===t.length)return e();const n=document.getElementsByTagName("link");return Promise.all(t.map(e=>{if(e=function(e,t){return new URL(e,t).href}(e,r),e in Do)return;Do[e]=!0;const t=e.endsWith(".css"),i=t?'[rel="stylesheet"]':"";if(!!r)for(let r=n.length-1;r>=0;r--){const i=n[r];if(i.href===e&&(!t||"stylesheet"===i.rel))return}else if(document.querySelector(`link[href="${e}"]${i}`))return;const o=document.createElement("link");return o.rel=t?"stylesheet":"modulepreload",t||(o.as="script",o.crossOrigin=""),o.href=e,document.head.appendChild(o),t?new Promise((t,r)=>{o.addEventListener("load",t),o.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${e}`)))}):void 0})).then(()=>e()).catch(e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e})};var No={exports:{}};var zo={exports:{}};const Ho=t(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var Fo;function Go(){return Fo||(Fo=1,zo.exports=function(){var e=e||function(e,t){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==r&&r.crypto&&(n=r.crypto),!n)try{n=Ho}catch(v){}var i=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(v){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(v){}}throw new Error("Native crypto module could not be used to get secure random number.")},o=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),s={},a=s.lib={},c=a.Base={extend:function(e){var t=o(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},u=a.WordArray=c.extend({init:function(e,r){e=this.words=e||[],this.sigBytes=r!=t?r:4*e.length},toString:function(e){return(e||h).stringify(this)},concat:function(e){var t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(var o=0;o<i;o++){var s=r[o>>>2]>>>24-o%4*8&255;t[n+o>>>2]|=s<<24-(n+o)%4*8}else for(var a=0;a<i;a+=4)t[n+a>>>2]=r[a>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=c.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(i());return new u.init(t,e)}}),l=s.enc={},h=l.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var o=t[i>>>2]>>>24-i%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new u.init(r,t/2)}},d=l.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var o=t[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(o))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new u.init(r,t)}},f=l.Utf8={stringify:function(e){try{return decodeURIComponent(escape(d.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return d.parse(unescape(encodeURIComponent(e)))}},p=a.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=f.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,n=this._data,i=n.words,o=n.sigBytes,s=this.blockSize,a=o/(4*s),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*s,l=e.min(4*c,o);if(c){for(var h=0;h<c;h+=s)this._doProcessBlock(i,h);r=i.splice(0,c),n.sigBytes-=l}return new u.init(r,l)},clone:function(){var e=c.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});a.Hasher=p.extend({cfg:c.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new g.HMAC.init(e,r).finalize(t)}}});var g=s.algo={};return s}(Math);return e}()),zo.exports}var qo,Wo={exports:{}};function Ko(){return qo||(qo=1,Wo.exports=function(e){return n=(r=e).lib,i=n.Base,o=n.WordArray,(s=r.x64={}).Word=i.extend({init:function(e,t){this.high=e,this.low=t}}),s.WordArray=i.extend({init:function(e,r){e=this.words=e||[],this.sigBytes=r!=t?r:8*e.length},toX32:function(){for(var e=this.words,t=e.length,r=[],n=0;n<t;n++){var i=e[n];r.push(i.high),r.push(i.low)}return o.create(r,this.sigBytes)},clone:function(){for(var e=i.clone.call(this),t=e.words=this.words.slice(0),r=t.length,n=0;n<r;n++)t[n]=t[n].clone();return e}}),e;var t,r,n,i,o,s}(Go())),Wo.exports}var Jo,Vo={exports:{}};function Xo(){return Jo||(Jo=1,Vo.exports=function(e){return function(){if("function"==typeof ArrayBuffer){var t=e.lib.WordArray,r=t.init,n=t.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,n=[],i=0;i<t;i++)n[i>>>2]|=e[i]<<24-i%4*8;r.call(this,n,t)}else r.apply(this,arguments)};n.prototype=t}}(),e.lib.WordArray}(Go())),Vo.exports}var Yo,Qo={exports:{}};function Zo(){return Yo||(Yo=1,Qo.exports=function(e){return function(){var t=e,r=t.lib.WordArray,n=t.enc;function i(e){return e<<8&4278255360|e>>>8&16711935}n.Utf16=n.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i+=2){var o=t[i>>>2]>>>16-i%4*8&65535;n.push(String.fromCharCode(o))}return n.join("")},parse:function(e){for(var t=e.length,n=[],i=0;i<t;i++)n[i>>>1]|=e.charCodeAt(i)<<16-i%2*16;return r.create(n,2*t)}},n.Utf16LE={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],o=0;o<r;o+=2){var s=i(t[o>>>2]>>>16-o%4*8&65535);n.push(String.fromCharCode(s))}return n.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o++)n[o>>>1]|=i(e.charCodeAt(o)<<16-o%2*16);return r.create(n,2*t)}}}(),e.enc.Utf16}(Go())),Qo.exports}var es,ts={exports:{}};function rs(){return es||(es=1,ts.exports=function(e){return function(){var t=e,r=t.lib.WordArray;function n(e,t,n){for(var i=[],o=0,s=0;s<t;s++)if(s%4){var a=n[e.charCodeAt(s-1)]<<s%4*2|n[e.charCodeAt(s)]>>>6-s%4*2;i[o>>>2]|=a<<24-o%4*8,o++}return r.create(i,o)}t.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var i=[],o=0;o<r;o+=3)for(var s=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,a=0;a<4&&o+.75*a<r;a++)i.push(n.charAt(s>>>6*(3-a)&63));var c=n.charAt(64);if(c)for(;i.length%4;)i.push(c);return i.join("")},parse:function(e){var t=e.length,r=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var o=0;o<r.length;o++)i[r.charCodeAt(o)]=o}var s=r.charAt(64);if(s){var a=e.indexOf(s);-1!==a&&(t=a)}return n(e,t,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),e.enc.Base64}(Go())),ts.exports}var ns,is={exports:{}};function os(){return ns||(ns=1,is.exports=function(e){return function(){var t=e,r=t.lib.WordArray;function n(e,t,n){for(var i=[],o=0,s=0;s<t;s++)if(s%4){var a=n[e.charCodeAt(s-1)]<<s%4*2|n[e.charCodeAt(s)]>>>6-s%4*2;i[o>>>2]|=a<<24-o%4*8,o++}return r.create(i,o)}t.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var r=e.words,n=e.sigBytes,i=t?this._safe_map:this._map;e.clamp();for(var o=[],s=0;s<n;s+=3)for(var a=(r[s>>>2]>>>24-s%4*8&255)<<16|(r[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|r[s+2>>>2]>>>24-(s+2)%4*8&255,c=0;c<4&&s+.75*c<n;c++)o.push(i.charAt(a>>>6*(3-c)&63));var u=i.charAt(64);if(u)for(;o.length%4;)o.push(u);return o.join("")},parse:function(e,t){void 0===t&&(t=!0);var r=e.length,i=t?this._safe_map:this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var s=0;s<i.length;s++)o[i.charCodeAt(s)]=s}var a=i.charAt(64);if(a){var c=e.indexOf(a);-1!==c&&(r=c)}return n(e,r,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),e.enc.Base64url}(Go())),is.exports}var ss,as={exports:{}};function cs(){return ss||(ss=1,as.exports=function(e){return function(t){var r=e,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.algo,a=[];!function(){for(var e=0;e<64;e++)a[e]=4294967296*t.abs(t.sin(e+1))|0}();var c=s.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var n=t+r,i=e[n];e[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o=this._hash.words,s=e[t+0],c=e[t+1],f=e[t+2],p=e[t+3],g=e[t+4],v=e[t+5],y=e[t+6],_=e[t+7],b=e[t+8],w=e[t+9],m=e[t+10],k=e[t+11],E=e[t+12],S=e[t+13],O=e[t+14],x=e[t+15],T=o[0],P=o[1],j=o[2],A=o[3];T=u(T,P,j,A,s,7,a[0]),A=u(A,T,P,j,c,12,a[1]),j=u(j,A,T,P,f,17,a[2]),P=u(P,j,A,T,p,22,a[3]),T=u(T,P,j,A,g,7,a[4]),A=u(A,T,P,j,v,12,a[5]),j=u(j,A,T,P,y,17,a[6]),P=u(P,j,A,T,_,22,a[7]),T=u(T,P,j,A,b,7,a[8]),A=u(A,T,P,j,w,12,a[9]),j=u(j,A,T,P,m,17,a[10]),P=u(P,j,A,T,k,22,a[11]),T=u(T,P,j,A,E,7,a[12]),A=u(A,T,P,j,S,12,a[13]),j=u(j,A,T,P,O,17,a[14]),T=l(T,P=u(P,j,A,T,x,22,a[15]),j,A,c,5,a[16]),A=l(A,T,P,j,y,9,a[17]),j=l(j,A,T,P,k,14,a[18]),P=l(P,j,A,T,s,20,a[19]),T=l(T,P,j,A,v,5,a[20]),A=l(A,T,P,j,m,9,a[21]),j=l(j,A,T,P,x,14,a[22]),P=l(P,j,A,T,g,20,a[23]),T=l(T,P,j,A,w,5,a[24]),A=l(A,T,P,j,O,9,a[25]),j=l(j,A,T,P,p,14,a[26]),P=l(P,j,A,T,b,20,a[27]),T=l(T,P,j,A,S,5,a[28]),A=l(A,T,P,j,f,9,a[29]),j=l(j,A,T,P,_,14,a[30]),T=h(T,P=l(P,j,A,T,E,20,a[31]),j,A,v,4,a[32]),A=h(A,T,P,j,b,11,a[33]),j=h(j,A,T,P,k,16,a[34]),P=h(P,j,A,T,O,23,a[35]),T=h(T,P,j,A,c,4,a[36]),A=h(A,T,P,j,g,11,a[37]),j=h(j,A,T,P,_,16,a[38]),P=h(P,j,A,T,m,23,a[39]),T=h(T,P,j,A,S,4,a[40]),A=h(A,T,P,j,s,11,a[41]),j=h(j,A,T,P,p,16,a[42]),P=h(P,j,A,T,y,23,a[43]),T=h(T,P,j,A,w,4,a[44]),A=h(A,T,P,j,E,11,a[45]),j=h(j,A,T,P,x,16,a[46]),T=d(T,P=h(P,j,A,T,f,23,a[47]),j,A,s,6,a[48]),A=d(A,T,P,j,_,10,a[49]),j=d(j,A,T,P,O,15,a[50]),P=d(P,j,A,T,v,21,a[51]),T=d(T,P,j,A,E,6,a[52]),A=d(A,T,P,j,p,10,a[53]),j=d(j,A,T,P,m,15,a[54]),P=d(P,j,A,T,c,21,a[55]),T=d(T,P,j,A,b,6,a[56]),A=d(A,T,P,j,x,10,a[57]),j=d(j,A,T,P,y,15,a[58]),P=d(P,j,A,T,S,21,a[59]),T=d(T,P,j,A,g,6,a[60]),A=d(A,T,P,j,k,10,a[61]),j=d(j,A,T,P,f,15,a[62]),P=d(P,j,A,T,w,21,a[63]),o[0]=o[0]+T|0,o[1]=o[1]+P|0,o[2]=o[2]+j|0,o[3]=o[3]+A|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;r[i>>>5]|=128<<24-i%32;var o=t.floor(n/4294967296),s=n;r[15+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),r[14+(i+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),e.sigBytes=4*(r.length+1),this._process();for(var a=this._hash,c=a.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return a},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,r,n,i,o,s){var a=e+(t&r|~t&n)+i+s;return(a<<o|a>>>32-o)+t}function l(e,t,r,n,i,o,s){var a=e+(t&n|r&~n)+i+s;return(a<<o|a>>>32-o)+t}function h(e,t,r,n,i,o,s){var a=e+(t^r^n)+i+s;return(a<<o|a>>>32-o)+t}function d(e,t,r,n,i,o,s){var a=e+(r^(t|~n))+i+s;return(a<<o|a>>>32-o)+t}r.MD5=o._createHelper(c),r.HmacMD5=o._createHmacHelper(c)}(Math),e.MD5}(Go())),as.exports}var us,ls={exports:{}};function hs(){return us||(us=1,ls.exports=function(e){return r=(t=e).lib,n=r.WordArray,i=r.Hasher,o=t.algo,s=[],a=o.SHA1=i.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],a=r[3],c=r[4],u=0;u<80;u++){if(u<16)s[u]=0|e[t+u];else{var l=s[u-3]^s[u-8]^s[u-14]^s[u-16];s[u]=l<<1|l>>>31}var h=(n<<5|n>>>27)+c+s[u];h+=u<20?1518500249+(i&o|~i&a):u<40?1859775393+(i^o^a):u<60?(i&o|i&a|o&a)-1894007588:(i^o^a)-899497514,c=a,a=o,o=i<<30|i>>>2,i=n,n=h}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(n+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}}),t.SHA1=i._createHelper(a),t.HmacSHA1=i._createHmacHelper(a),e.SHA1;var t,r,n,i,o,s,a}(Go())),ls.exports}var ds,fs={exports:{}};function ps(){return ds||(ds=1,fs.exports=function(e){return function(t){var r=e,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.algo,a=[],c=[];!function(){function e(e){for(var r=t.sqrt(e),n=2;n<=r;n++)if(!(e%n))return!1;return!0}function r(e){return 4294967296*(e-(0|e))|0}for(var n=2,i=0;i<64;)e(n)&&(i<8&&(a[i]=r(t.pow(n,.5))),c[i]=r(t.pow(n,1/3)),i++),n++}();var u=[],l=s.SHA256=o.extend({_doReset:function(){this._hash=new i.init(a.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],s=r[3],a=r[4],l=r[5],h=r[6],d=r[7],f=0;f<64;f++){if(f<16)u[f]=0|e[t+f];else{var p=u[f-15],g=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,v=u[f-2],y=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;u[f]=g+u[f-7]+y+u[f-16]}var _=n&i^n&o^i&o,b=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),w=d+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&l^~a&h)+c[f]+u[f];d=h,h=l,l=a,a=s+w|0,s=o,o=i,i=n,n=w+(b+_)|0}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+a|0,r[5]=r[5]+l|0,r[6]=r[6]+h|0,r[7]=r[7]+d|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;return r[i>>>5]|=128<<24-i%32,r[14+(i+64>>>9<<4)]=t.floor(n/4294967296),r[15+(i+64>>>9<<4)]=n,e.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});r.SHA256=o._createHelper(l),r.HmacSHA256=o._createHmacHelper(l)}(Math),e.SHA256}(Go())),fs.exports}var gs,vs={exports:{}};var ys,_s={exports:{}};function bs(){return ys||(ys=1,_s.exports=function(e){return function(){var t=e,r=t.lib.Hasher,n=t.x64,i=n.Word,o=n.WordArray,s=t.algo;function a(){return i.create.apply(i,arguments)}var c=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],u=[];!function(){for(var e=0;e<80;e++)u[e]=a()}();var l=s.SHA512=r.extend({_doReset:function(){this._hash=new o.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],s=r[3],a=r[4],l=r[5],h=r[6],d=r[7],f=n.high,p=n.low,g=i.high,v=i.low,y=o.high,_=o.low,b=s.high,w=s.low,m=a.high,k=a.low,E=l.high,S=l.low,O=h.high,x=h.low,T=d.high,P=d.low,j=f,A=p,C=g,R=v,B=y,I=_,M=b,L=w,$=m,D=k,U=E,N=S,z=O,H=x,F=T,G=P,q=0;q<80;q++){var W,K,J=u[q];if(q<16)K=J.high=0|e[t+2*q],W=J.low=0|e[t+2*q+1];else{var V=u[q-15],X=V.high,Y=V.low,Q=(X>>>1|Y<<31)^(X>>>8|Y<<24)^X>>>7,Z=(Y>>>1|X<<31)^(Y>>>8|X<<24)^(Y>>>7|X<<25),ee=u[q-2],te=ee.high,re=ee.low,ne=(te>>>19|re<<13)^(te<<3|re>>>29)^te>>>6,ie=(re>>>19|te<<13)^(re<<3|te>>>29)^(re>>>6|te<<26),oe=u[q-7],se=oe.high,ae=oe.low,ce=u[q-16],ue=ce.high,le=ce.low;K=(K=(K=Q+se+((W=Z+ae)>>>0<Z>>>0?1:0))+ne+((W+=ie)>>>0<ie>>>0?1:0))+ue+((W+=le)>>>0<le>>>0?1:0),J.high=K,J.low=W}var he,de=$&U^~$&z,fe=D&N^~D&H,pe=j&C^j&B^C&B,ge=A&R^A&I^R&I,ve=(j>>>28|A<<4)^(j<<30|A>>>2)^(j<<25|A>>>7),ye=(A>>>28|j<<4)^(A<<30|j>>>2)^(A<<25|j>>>7),_e=($>>>14|D<<18)^($>>>18|D<<14)^($<<23|D>>>9),be=(D>>>14|$<<18)^(D>>>18|$<<14)^(D<<23|$>>>9),we=c[q],me=we.high,ke=we.low,Ee=F+_e+((he=G+be)>>>0<G>>>0?1:0),Se=ye+ge;F=z,G=H,z=U,H=N,U=$,N=D,$=M+(Ee=(Ee=(Ee=Ee+de+((he+=fe)>>>0<fe>>>0?1:0))+me+((he+=ke)>>>0<ke>>>0?1:0))+K+((he+=W)>>>0<W>>>0?1:0))+((D=L+he|0)>>>0<L>>>0?1:0)|0,M=B,L=I,B=C,I=R,C=j,R=A,j=Ee+(ve+pe+(Se>>>0<ye>>>0?1:0))+((A=he+Se|0)>>>0<he>>>0?1:0)|0}p=n.low=p+A,n.high=f+j+(p>>>0<A>>>0?1:0),v=i.low=v+R,i.high=g+C+(v>>>0<R>>>0?1:0),_=o.low=_+I,o.high=y+B+(_>>>0<I>>>0?1:0),w=s.low=w+L,s.high=b+M+(w>>>0<L>>>0?1:0),k=a.low=k+D,a.high=m+$+(k>>>0<D>>>0?1:0),S=l.low=S+N,l.high=E+U+(S>>>0<N>>>0?1:0),x=h.low=x+H,h.high=O+z+(x>>>0<H>>>0?1:0),P=d.low=P+G,d.high=T+F+(P>>>0<G>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[30+(n+128>>>10<<5)]=Math.floor(r/4294967296),t[31+(n+128>>>10<<5)]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});t.SHA512=r._createHelper(l),t.HmacSHA512=r._createHmacHelper(l)}(),e.SHA512}(Go(),Ko())),_s.exports}var ws,ms={exports:{}};var ks,Es={exports:{}};function Ss(){return ks||(ks=1,Es.exports=function(e){return function(t){var r=e,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.x64.Word,a=r.algo,c=[],u=[],l=[];!function(){for(var e=1,t=0,r=0;r<24;r++){c[e+5*t]=(r+1)*(r+2)/2%64;var n=(2*e+3*t)%5;e=t%5,t=n}for(e=0;e<5;e++)for(t=0;t<5;t++)u[e+5*t]=t+(2*e+3*t)%5*5;for(var i=1,o=0;o<24;o++){for(var a=0,h=0,d=0;d<7;d++){if(1&i){var f=(1<<d)-1;f<32?h^=1<<f:a^=1<<f-32}128&i?i=i<<1^113:i<<=1}l[o]=s.create(a,h)}}();var h=[];!function(){for(var e=0;e<25;e++)h[e]=s.create()}();var d=a.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new s.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,n=this.blockSize/2,i=0;i<n;i++){var o=e[t+2*i],s=e[t+2*i+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),(P=r[i]).high^=s,P.low^=o}for(var a=0;a<24;a++){for(var d=0;d<5;d++){for(var f=0,p=0,g=0;g<5;g++)f^=(P=r[d+5*g]).high,p^=P.low;var v=h[d];v.high=f,v.low=p}for(d=0;d<5;d++){var y=h[(d+4)%5],_=h[(d+1)%5],b=_.high,w=_.low;for(f=y.high^(b<<1|w>>>31),p=y.low^(w<<1|b>>>31),g=0;g<5;g++)(P=r[d+5*g]).high^=f,P.low^=p}for(var m=1;m<25;m++){var k=(P=r[m]).high,E=P.low,S=c[m];S<32?(f=k<<S|E>>>32-S,p=E<<S|k>>>32-S):(f=E<<S-32|k>>>64-S,p=k<<S-32|E>>>64-S);var O=h[u[m]];O.high=f,O.low=p}var x=h[0],T=r[0];for(x.high=T.high,x.low=T.low,d=0;d<5;d++)for(g=0;g<5;g++){var P=r[m=d+5*g],j=h[m],A=h[(d+1)%5+5*g],C=h[(d+2)%5+5*g];P.high=j.high^~A.high&C.high,P.low=j.low^~A.low&C.low}P=r[0];var R=l[a];P.high^=R.high,P.low^=R.low}},_doFinalize:function(){var e=this._data,r=e.words;this._nDataBytes;var n=8*e.sigBytes,o=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(t.ceil((n+1)/o)*o>>>5)-1]|=128,e.sigBytes=4*r.length,this._process();for(var s=this._state,a=this.cfg.outputLength/8,c=a/8,u=[],l=0;l<c;l++){var h=s[l],d=h.high,f=h.low;d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),u.push(f),u.push(d)}return new i.init(u,a)},clone:function(){for(var e=o.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}});r.SHA3=o._createHelper(d),r.HmacSHA3=o._createHmacHelper(d)}(Math),e.SHA3}(Go(),Ko())),Es.exports}var Os,xs={exports:{}};var Ts,Ps={exports:{}};function js(){return Ts||(Ts=1,Ps.exports=function(e){var t,r,n;r=(t=e).lib.Base,n=t.enc.Utf8,t.algo.HMAC=r.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=n.parse(t));var r=e.blockSize,i=4*r;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var o=this._oKey=t.clone(),s=this._iKey=t.clone(),a=o.words,c=s.words,u=0;u<r;u++)a[u]^=1549556828,c[u]^=909522486;o.sigBytes=s.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})}(Go())),Ps.exports}var As,Cs={exports:{}};var Rs,Bs={exports:{}};function Is(){return Rs||(Rs=1,Bs.exports=function(e){return r=(t=e).lib,n=r.Base,i=r.WordArray,o=t.algo,s=o.MD5,a=o.EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:s,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,n=this.cfg,o=n.hasher.create(),s=i.create(),a=s.words,c=n.keySize,u=n.iterations;a.length<c;){r&&o.update(r),r=o.update(e).finalize(t),o.reset();for(var l=1;l<u;l++)r=o.finalize(r),o.reset();s.concat(r)}return s.sigBytes=4*c,s}}),t.EvpKDF=function(e,t,r){return a.create(r).compute(e,t)},e.EvpKDF;var t,r,n,i,o,s,a}(Go(),hs(),js())),Bs.exports}var Ms,Ls={exports:{}};function $s(){return Ms||(Ms=1,Ls.exports=function(e){e.lib.Cipher||function(t){var r=e,n=r.lib,i=n.Base,o=n.WordArray,s=n.BufferedBlockAlgorithm,a=r.enc;a.Utf8;var c=a.Base64,u=r.algo.EvpKDF,l=n.Cipher=s.extend({cfg:i.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){s.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?b:y}return function(t){return{encrypt:function(r,n,i){return e(n).encrypt(t,r,n,i)},decrypt:function(r,n,i){return e(n).decrypt(t,r,n,i)}}}}()});n.StreamCipher=l.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var h=r.mode={},d=n.BlockCipherMode=i.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),f=h.CBC=function(){var e=d.extend();function r(e,r,n){var i,o=this._iv;o?(i=o,this._iv=t):i=this._prevBlock;for(var s=0;s<n;s++)e[r+s]^=i[s]}return e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize;r.call(this,e,t,i),n.encryptBlock(e,t),this._prevBlock=e.slice(t,t+i)}}),e.Decryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize,o=e.slice(t,t+i);n.decryptBlock(e,t),r.call(this,e,t,i),this._prevBlock=o}}),e}(),p=(r.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,s=[],a=0;a<n;a+=4)s.push(i);var c=o.create(s,n);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}};n.BlockCipher=l.extend({cfg:l.cfg.extend({mode:f,padding:p}),reset:function(){var e;l.reset.call(this);var t=this.cfg,r=t.iv,n=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=n.createEncryptor:(e=n.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(n,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4});var g=n.CipherParams=i.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),v=(r.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?o.create([1398893684,1701076831]).concat(r).concat(t):t).toString(c)},parse:function(e){var t,r=c.parse(e),n=r.words;return 1398893684==n[0]&&1701076831==n[1]&&(t=o.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),g.create({ciphertext:r,salt:t})}},y=n.SerializableCipher=i.extend({cfg:i.extend({format:v}),encrypt:function(e,t,r,n){n=this.cfg.extend(n);var i=e.createEncryptor(r,n),o=i.finalize(t),s=i.cfg;return g.create({ciphertext:o,key:r,iv:s.iv,algorithm:e,mode:s.mode,padding:s.padding,blockSize:e.blockSize,formatter:n.format})},decrypt:function(e,t,r,n){return n=this.cfg.extend(n),t=this._parse(t,n.format),e.createDecryptor(r,n).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),_=(r.kdf={}).OpenSSL={execute:function(e,t,r,n,i){if(n||(n=o.random(8)),i)s=u.create({keySize:t+r,hasher:i}).compute(e,n);else var s=u.create({keySize:t+r}).compute(e,n);var a=o.create(s.words.slice(t),4*r);return s.sigBytes=4*t,g.create({key:s,iv:a,salt:n})}},b=n.PasswordBasedCipher=y.extend({cfg:y.cfg.extend({kdf:_}),encrypt:function(e,t,r,n){var i=(n=this.cfg.extend(n)).kdf.execute(r,e.keySize,e.ivSize,n.salt,n.hasher);n.iv=i.iv;var o=y.encrypt.call(this,e,t,i.key,n);return o.mixIn(i),o},decrypt:function(e,t,r,n){n=this.cfg.extend(n),t=this._parse(t,n.format);var i=n.kdf.execute(r,e.keySize,e.ivSize,t.salt,n.hasher);return n.iv=i.iv,y.decrypt.call(this,e,t,i.key,n)}})}()}(Go(),Is())),Ls.exports}var Ds,Us={exports:{}};function Ns(){return Ds||(Ds=1,Us.exports=function(e){return e.mode.CFB=function(){var t=e.lib.BlockCipherMode.extend();function r(e,t,r,n){var i,o=this._iv;o?(i=o.slice(0),this._iv=void 0):i=this._prevBlock,n.encryptBlock(i,0);for(var s=0;s<r;s++)e[t+s]^=i[s]}return t.Encryptor=t.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize;r.call(this,e,t,i,n),this._prevBlock=e.slice(t,t+i)}}),t.Decryptor=t.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize,o=e.slice(t,t+i);r.call(this,e,t,i,n),this._prevBlock=o}}),t}(),e.mode.CFB}(Go(),$s())),Us.exports}var zs,Hs={exports:{}};function Fs(){return zs||(zs=1,Hs.exports=function(e){return e.mode.CTR=(t=e.lib.BlockCipherMode.extend(),r=t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0);var s=o.slice(0);r.encryptBlock(s,0),o[n-1]=o[n-1]+1|0;for(var a=0;a<n;a++)e[t+a]^=s[a]}}),t.Decryptor=r,t),e.mode.CTR;var t,r}(Go(),$s())),Hs.exports}var Gs,qs={exports:{}};function Ws(){return Gs||(Gs=1,qs.exports=function(e){
/** @preserve
			 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
			 * derived from CryptoJS.mode.CTR
			 * <NAME_EMAIL>
			 */
return e.mode.CTRGladman=function(){var t=e.lib.BlockCipherMode.extend();function r(e){if(255&~(e>>24))e+=1<<24;else{var t=e>>16&255,r=e>>8&255,n=255&e;255===t?(t=0,255===r?(r=0,255===n?n=0:++n):++r):++t,e=0,e+=t<<16,e+=r<<8,e+=n}return e}function n(e){return 0===(e[0]=r(e[0]))&&(e[1]=r(e[1])),e}var i=t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,o=this._iv,s=this._counter;o&&(s=this._counter=o.slice(0),this._iv=void 0),n(s);var a=s.slice(0);r.encryptBlock(a,0);for(var c=0;c<i;c++)e[t+c]^=a[c]}});return t.Decryptor=i,t}(),e.mode.CTRGladman}(Go(),$s())),qs.exports}var Ks,Js={exports:{}};function Vs(){return Ks||(Ks=1,Js.exports=function(e){return e.mode.OFB=(t=e.lib.BlockCipherMode.extend(),r=t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._keystream;i&&(o=this._keystream=i.slice(0),this._iv=void 0),r.encryptBlock(o,0);for(var s=0;s<n;s++)e[t+s]^=o[s]}}),t.Decryptor=r,t),e.mode.OFB;var t,r}(Go(),$s())),Js.exports}var Xs,Ys={exports:{}};var Qs,Zs={exports:{}};var ea,ta={exports:{}};var ra,na={exports:{}};var ia,oa={exports:{}};var sa,aa={exports:{}};var ca,ua={exports:{}};var la,ha={exports:{}};var da,fa={exports:{}};function pa(){return da||(da=1,fa.exports=function(e){return function(){var t=e,r=t.lib,n=r.WordArray,i=r.BlockCipher,o=t.algo,s=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],u=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],h=o.DES=i.extend({_doReset:function(){for(var e=this._key.words,t=[],r=0;r<56;r++){var n=s[r]-1;t[r]=e[n>>>5]>>>31-n%32&1}for(var i=this._subKeys=[],o=0;o<16;o++){var u=i[o]=[],l=c[o];for(r=0;r<24;r++)u[r/6|0]|=t[(a[r]-1+l)%28]<<31-r%6,u[4+(r/6|0)]|=t[28+(a[r+24]-1+l)%28]<<31-r%6;for(u[0]=u[0]<<1|u[0]>>>31,r=1;r<7;r++)u[r]=u[r]>>>4*(r-1)+3;u[7]=u[7]<<5|u[7]>>>27}var h=this._invSubKeys=[];for(r=0;r<16;r++)h[r]=i[15-r]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,r){this._lBlock=e[t],this._rBlock=e[t+1],d.call(this,4,252645135),d.call(this,16,65535),f.call(this,2,858993459),f.call(this,8,16711935),d.call(this,1,1431655765);for(var n=0;n<16;n++){for(var i=r[n],o=this._lBlock,s=this._rBlock,a=0,c=0;c<8;c++)a|=u[c][((s^i[c])&l[c])>>>0];this._lBlock=s,this._rBlock=o^a}var h=this._lBlock;this._lBlock=this._rBlock,this._rBlock=h,d.call(this,1,1431655765),f.call(this,8,16711935),f.call(this,2,858993459),d.call(this,16,65535),d.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function d(e,t){var r=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=r,this._lBlock^=r<<e}function f(e,t){var r=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=r,this._rBlock^=r<<e}t.DES=i._createHelper(h);var p=o.TripleDES=i.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),r=e.length<4?e.slice(0,2):e.slice(2,4),i=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=h.createEncryptor(n.create(t)),this._des2=h.createEncryptor(n.create(r)),this._des3=h.createEncryptor(n.create(i))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});t.TripleDES=i._createHelper(p)}(),e.TripleDES}(Go(),rs(),cs(),Is(),$s())),fa.exports}var ga,va={exports:{}};var ya,_a={exports:{}};var ba,wa={exports:{}};var ma,ka={exports:{}};function Ea(){return ma||(ma=1,ka.exports=function(e){return function(){var t=e,r=t.lib.BlockCipher,n=t.algo;const i=16,o=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],s=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var a={pbox:[],sbox:[]};function c(e,t){let r=t>>24&255,n=t>>16&255,i=t>>8&255,o=255&t,s=e.sbox[0][r]+e.sbox[1][n];return s^=e.sbox[2][i],s+=e.sbox[3][o],s}function u(e,t,r){let n,o=t,s=r;for(let a=0;a<i;++a)o^=e.pbox[a],s=c(e,o)^s,n=o,o=s,s=n;return n=o,o=s,s=n,s^=e.pbox[i],o^=e.pbox[i+1],{left:o,right:s}}function l(e,t,r){let n,o=t,s=r;for(let a=i+1;a>1;--a)o^=e.pbox[a],s=c(e,o)^s,n=o,o=s,s=n;return n=o,o=s,s=n,s^=e.pbox[1],o^=e.pbox[0],{left:o,right:s}}function h(e,t,r){for(let i=0;i<4;i++){e.sbox[i]=[];for(let t=0;t<256;t++)e.sbox[i][t]=s[i][t]}let n=0;for(let s=0;s<i+2;s++)e.pbox[s]=o[s]^t[n],n++,n>=r&&(n=0);let a=0,c=0,l=0;for(let o=0;o<i+2;o+=2)l=u(e,a,c),a=l.left,c=l.right,e.pbox[o]=a,e.pbox[o+1]=c;for(let i=0;i<4;i++)for(let t=0;t<256;t+=2)l=u(e,a,c),a=l.left,c=l.right,e.sbox[i][t]=a,e.sbox[i][t+1]=c;return!0}var d=n.Blowfish=r.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4;h(a,t,r)}},encryptBlock:function(e,t){var r=u(a,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},decryptBlock:function(e,t){var r=l(a,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},blockSize:2,keySize:4,ivSize:2});t.Blowfish=r._createHelper(d)}(),e.Blowfish}(Go(),rs(),cs(),Is(),$s())),ka.exports}No.exports=function(e){return e}(Go(),Ko(),Xo(),Zo(),rs(),os(),cs(),hs(),ps(),gs||(gs=1,vs.exports=function(e){return r=(t=e).lib.WordArray,n=t.algo,i=n.SHA256,o=n.SHA224=i.extend({_doReset:function(){this._hash=new r.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=i._doFinalize.call(this);return e.sigBytes-=4,e}}),t.SHA224=i._createHelper(o),t.HmacSHA224=i._createHmacHelper(o),e.SHA224;var t,r,n,i,o}(Go(),ps())),bs(),ws||(ws=1,ms.exports=function(e){return r=(t=e).x64,n=r.Word,i=r.WordArray,o=t.algo,s=o.SHA512,a=o.SHA384=s.extend({_doReset:function(){this._hash=new i.init([new n.init(3418070365,3238371032),new n.init(1654270250,914150663),new n.init(2438529370,812702999),new n.init(355462360,4144912697),new n.init(1731405415,4290775857),new n.init(2394180231,1750603025),new n.init(3675008525,1694076839),new n.init(1203062813,3204075428)])},_doFinalize:function(){var e=s._doFinalize.call(this);return e.sigBytes-=16,e}}),t.SHA384=s._createHelper(a),t.HmacSHA384=s._createHmacHelper(a),e.SHA384;var t,r,n,i,o,s,a}(Go(),Ko(),bs())),Ss(),Os||(Os=1,xs.exports=function(e){
/** @preserve
			(c) 2012 by Cédric Mesnil. All rights reserved.

			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
			*/
return function(){var t=e,r=t.lib,n=r.WordArray,i=r.Hasher,o=t.algo,s=n.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),a=n.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),c=n.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),u=n.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),l=n.create([0,1518500249,1859775393,2400959708,2840853838]),h=n.create([1352829926,1548603684,1836072691,2053994217,0]),d=o.RIPEMD160=i.extend({_doReset:function(){this._hash=n.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var n=t+r,i=e[n];e[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o,d,b,w,m,k,E,S,O,x,T,P=this._hash.words,j=l.words,A=h.words,C=s.words,R=a.words,B=c.words,I=u.words;for(k=o=P[0],E=d=P[1],S=b=P[2],O=w=P[3],x=m=P[4],r=0;r<80;r+=1)T=o+e[t+C[r]]|0,T+=r<16?f(d,b,w)+j[0]:r<32?p(d,b,w)+j[1]:r<48?g(d,b,w)+j[2]:r<64?v(d,b,w)+j[3]:y(d,b,w)+j[4],T=(T=_(T|=0,B[r]))+m|0,o=m,m=w,w=_(b,10),b=d,d=T,T=k+e[t+R[r]]|0,T+=r<16?y(E,S,O)+A[0]:r<32?v(E,S,O)+A[1]:r<48?g(E,S,O)+A[2]:r<64?p(E,S,O)+A[3]:f(E,S,O)+A[4],T=(T=_(T|=0,I[r]))+x|0,k=x,x=O,O=_(S,10),S=E,E=T;T=P[1]+b+O|0,P[1]=P[2]+w+x|0,P[2]=P[3]+m+k|0,P[3]=P[4]+o+E|0,P[4]=P[0]+d+S|0,P[0]=T},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process();for(var i=this._hash,o=i.words,s=0;s<5;s++){var a=o[s];o[s]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return i},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function f(e,t,r){return e^t^r}function p(e,t,r){return e&t|~e&r}function g(e,t,r){return(e|~t)^r}function v(e,t,r){return e&r|t&~r}function y(e,t,r){return e^(t|~r)}function _(e,t){return e<<t|e>>>32-t}t.RIPEMD160=i._createHelper(d),t.HmacRIPEMD160=i._createHmacHelper(d)}(),e.RIPEMD160}(Go())),js(),As||(As=1,Cs.exports=function(e){return n=(r=(t=e).lib).Base,i=r.WordArray,s=(o=t.algo).SHA256,a=o.HMAC,c=o.PBKDF2=n.extend({cfg:n.extend({keySize:4,hasher:s,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,n=a.create(r.hasher,e),o=i.create(),s=i.create([1]),c=o.words,u=s.words,l=r.keySize,h=r.iterations;c.length<l;){var d=n.update(t).finalize(s);n.reset();for(var f=d.words,p=f.length,g=d,v=1;v<h;v++){g=n.finalize(g),n.reset();for(var y=g.words,_=0;_<p;_++)f[_]^=y[_]}o.concat(d),u[0]++}return o.sigBytes=4*l,o}}),t.PBKDF2=function(e,t,r){return c.create(r).compute(e,t)},e.PBKDF2;var t,r,n,i,o,s,a,c}(Go(),ps(),js())),Is(),$s(),Ns(),Fs(),Ws(),Vs(),Xs||(Xs=1,Ys.exports=function(e){return e.mode.ECB=((t=e.lib.BlockCipherMode.extend()).Encryptor=t.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),t.Decryptor=t.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),t),e.mode.ECB;var t}(Go(),$s())),Qs||(Qs=1,Zs.exports=function(e){return e.pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,n=4*t,i=n-r%n,o=r+i-1;e.clamp(),e.words[o>>>2]|=i<<24-o%4*8,e.sigBytes+=i},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Ansix923}(Go(),$s())),ea||(ea=1,ta.exports=function(e){return e.pad.Iso10126={pad:function(t,r){var n=4*r,i=n-t.sigBytes%n;t.concat(e.lib.WordArray.random(i-1)).concat(e.lib.WordArray.create([i<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Iso10126}(Go(),$s())),ra||(ra=1,na.exports=function(e){return e.pad.Iso97971={pad:function(t,r){t.concat(e.lib.WordArray.create([2147483648],1)),e.pad.ZeroPadding.pad(t,r)},unpad:function(t){e.pad.ZeroPadding.unpad(t),t.sigBytes--}},e.pad.Iso97971}(Go(),$s())),ia||(ia=1,oa.exports=function(e){return e.pad.ZeroPadding={pad:function(e,t){var r=4*t;e.clamp(),e.sigBytes+=r-(e.sigBytes%r||r)},unpad:function(e){var t=e.words,r=e.sigBytes-1;for(r=e.sigBytes-1;r>=0;r--)if(t[r>>>2]>>>24-r%4*8&255){e.sigBytes=r+1;break}}},e.pad.ZeroPadding}(Go(),$s())),sa||(sa=1,aa.exports=function(e){return e.pad.NoPadding={pad:function(){},unpad:function(){}},e.pad.NoPadding}(Go(),$s())),ca||(ca=1,ua.exports=function(e){return r=(t=e).lib.CipherParams,n=t.enc.Hex,t.format.Hex={stringify:function(e){return e.ciphertext.toString(n)},parse:function(e){var t=n.parse(e);return r.create({ciphertext:t})}},e.format.Hex;var t,r,n}(Go(),$s())),la||(la=1,ha.exports=function(e){return function(){var t=e,r=t.lib.BlockCipher,n=t.algo,i=[],o=[],s=[],a=[],c=[],u=[],l=[],h=[],d=[],f=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var r=0,n=0;for(t=0;t<256;t++){var p=n^n<<1^n<<2^n<<3^n<<4;p=p>>>8^255&p^99,i[r]=p,o[p]=r;var g=e[r],v=e[g],y=e[v],_=257*e[p]^16843008*p;s[r]=_<<24|_>>>8,a[r]=_<<16|_>>>16,c[r]=_<<8|_>>>24,u[r]=_,_=16843009*y^65537*v^257*g^16843008*r,l[p]=_<<24|_>>>8,h[p]=_<<16|_>>>16,d[p]=_<<8|_>>>24,f[p]=_,r?(r=g^e[e[e[y^g]]],n^=e[e[n]]):r=n=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],g=n.AES=r.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,n=4*((this._nRounds=r+6)+1),o=this._keySchedule=[],s=0;s<n;s++)s<r?o[s]=t[s]:(u=o[s-1],s%r?r>6&&s%r==4&&(u=i[u>>>24]<<24|i[u>>>16&255]<<16|i[u>>>8&255]<<8|i[255&u]):(u=i[(u=u<<8|u>>>24)>>>24]<<24|i[u>>>16&255]<<16|i[u>>>8&255]<<8|i[255&u],u^=p[s/r|0]<<24),o[s]=o[s-r]^u);for(var a=this._invKeySchedule=[],c=0;c<n;c++){if(s=n-c,c%4)var u=o[s];else u=o[s-4];a[c]=c<4||s<=4?u:l[i[u>>>24]]^h[i[u>>>16&255]]^d[i[u>>>8&255]]^f[i[255&u]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,s,a,c,u,i)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,l,h,d,f,o),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,n,i,o,s,a){for(var c=this._nRounds,u=e[t]^r[0],l=e[t+1]^r[1],h=e[t+2]^r[2],d=e[t+3]^r[3],f=4,p=1;p<c;p++){var g=n[u>>>24]^i[l>>>16&255]^o[h>>>8&255]^s[255&d]^r[f++],v=n[l>>>24]^i[h>>>16&255]^o[d>>>8&255]^s[255&u]^r[f++],y=n[h>>>24]^i[d>>>16&255]^o[u>>>8&255]^s[255&l]^r[f++],_=n[d>>>24]^i[u>>>16&255]^o[l>>>8&255]^s[255&h]^r[f++];u=g,l=v,h=y,d=_}g=(a[u>>>24]<<24|a[l>>>16&255]<<16|a[h>>>8&255]<<8|a[255&d])^r[f++],v=(a[l>>>24]<<24|a[h>>>16&255]<<16|a[d>>>8&255]<<8|a[255&u])^r[f++],y=(a[h>>>24]<<24|a[d>>>16&255]<<16|a[u>>>8&255]<<8|a[255&l])^r[f++],_=(a[d>>>24]<<24|a[u>>>16&255]<<16|a[l>>>8&255]<<8|a[255&h])^r[f++],e[t]=g,e[t+1]=v,e[t+2]=y,e[t+3]=_},keySize:8});t.AES=r._createHelper(g)}(),e.AES}(Go(),rs(),cs(),Is(),$s())),pa(),ga||(ga=1,va.exports=function(e){return function(){var t=e,r=t.lib.StreamCipher,n=t.algo,i=n.RC4=r.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,n=this._S=[],i=0;i<256;i++)n[i]=i;i=0;for(var o=0;i<256;i++){var s=i%r,a=t[s>>>2]>>>24-s%4*8&255;o=(o+n[i]+a)%256;var c=n[i];n[i]=n[o],n[o]=c}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=o.call(this)},keySize:8,ivSize:0});function o(){for(var e=this._S,t=this._i,r=this._j,n=0,i=0;i<4;i++){r=(r+e[t=(t+1)%256])%256;var o=e[t];e[t]=e[r],e[r]=o,n|=e[(e[t]+e[r])%256]<<24-8*i}return this._i=t,this._j=r,n}t.RC4=r._createHelper(i);var s=n.RC4Drop=i.extend({cfg:i.cfg.extend({drop:192}),_doReset:function(){i._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)o.call(this)}});t.RC4Drop=r._createHelper(s)}(),e.RC4}(Go(),rs(),cs(),Is(),$s())),ya||(ya=1,_a.exports=function(e){return function(){var t=e,r=t.lib.StreamCipher,n=t.algo,i=[],o=[],s=[],a=n.Rabbit=r.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=0;r<4;r++)e[r]=16711935&(e[r]<<8|e[r]>>>24)|4278255360&(e[r]<<24|e[r]>>>8);var n=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,r=0;r<4;r++)c.call(this);for(r=0;r<8;r++)i[r]^=n[r+4&7];if(t){var o=t.words,s=o[0],a=o[1],u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),h=u>>>16|4294901760&l,d=l<<16|65535&u;for(i[0]^=u,i[1]^=h,i[2]^=l,i[3]^=d,i[4]^=u,i[5]^=h,i[6]^=l,i[7]^=d,r=0;r<4;r++)c.call(this)}},_doProcessBlock:function(e,t){var r=this._X;c.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)i[n]=16711935&(i[n]<<8|i[n]>>>24)|4278255360&(i[n]<<24|i[n]>>>8),e[t+n]^=i[n]},blockSize:4,ivSize:2});function c(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var n=e[r]+t[r],i=65535&n,a=n>>>16,c=((i*i>>>17)+i*a>>>15)+a*a,u=((4294901760&n)*n|0)+((65535&n)*n|0);s[r]=c^u}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}t.Rabbit=r._createHelper(a)}(),e.Rabbit}(Go(),rs(),cs(),Is(),$s())),ba||(ba=1,wa.exports=function(e){return function(){var t=e,r=t.lib.StreamCipher,n=t.algo,i=[],o=[],s=[],a=n.RabbitLegacy=r.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var i=0;i<4;i++)c.call(this);for(i=0;i<8;i++)n[i]^=r[i+4&7];if(t){var o=t.words,s=o[0],a=o[1],u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),h=u>>>16|4294901760&l,d=l<<16|65535&u;for(n[0]^=u,n[1]^=h,n[2]^=l,n[3]^=d,n[4]^=u,n[5]^=h,n[6]^=l,n[7]^=d,i=0;i<4;i++)c.call(this)}},_doProcessBlock:function(e,t){var r=this._X;c.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)i[n]=16711935&(i[n]<<8|i[n]>>>24)|4278255360&(i[n]<<24|i[n]>>>8),e[t+n]^=i[n]},blockSize:4,ivSize:2});function c(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var n=e[r]+t[r],i=65535&n,a=n>>>16,c=((i*i>>>17)+i*a>>>15)+a*a,u=((4294901760&n)*n|0)+((65535&n)*n|0);s[r]=c^u}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}t.RabbitLegacy=r._createHelper(a)}(),e.RabbitLegacy}(Go(),rs(),cs(),Is(),$s())),Ea());const Sa=e(No.exports);class Oa extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class xa extends Oa{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class Ta extends Oa{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class Pa extends Oa{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var ja;!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(ja||(ja={}));var Aa=globalThis&&globalThis.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{c(n.next(e))}catch(t){o(t)}}function a(e){try{c(n.throw(e))}catch(t){o(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(s,a)}c((n=n.apply(e,t||[])).next())})};class Ca{constructor(e,{headers:t={},customFetch:r,region:n=ja.Any}={}){this.url=e,this.headers=t,this.region=n,this.fetch=(e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Uo(()=>Promise.resolve().then(()=>Ga),void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)})(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return Aa(this,void 0,void 0,function*(){try{const{headers:n,method:i,body:o}=t;let s={},{region:a}=t;a||(a=this.region);const c=new URL(`${this.url}/${e}`);let u;a&&"any"!==a&&(s["x-region"]=a,c.searchParams.set("forceFunctionRegion",a)),o&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&("undefined"!=typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(s["Content-Type"]="application/octet-stream",u=o):"string"==typeof o?(s["Content-Type"]="text/plain",u=o):"undefined"!=typeof FormData&&o instanceof FormData?u=o:(s["Content-Type"]="application/json",u=JSON.stringify(o)));const l=yield this.fetch(c.toString(),{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},s),this.headers),n),body:u}).catch(e=>{throw new xa(e)}),h=l.headers.get("x-relay-error");if(h&&"true"===h)throw new Ta(l);if(!l.ok)throw new Pa(l);let d,f=(null!==(r=l.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return d="application/json"===f?yield l.json():"application/octet-stream"===f?yield l.blob():"text/event-stream"===f?l:"multipart/form-data"===f?yield l.formData():yield l.text(),{data:d,error:null,response:l}}catch(n){return{data:null,error:n,response:n instanceof Pa||n instanceof Ta?n.context:void 0}}})}}var Ra={},Ba={},Ia={},Ma={},La={},$a={},Da=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const Ua=Da.fetch,Na=Da.fetch.bind(Da),za=Da.Headers,Ha=Da.Request,Fa=Da.Response,Ga=Object.freeze(Object.defineProperty({__proto__:null,Headers:za,Request:Ha,Response:Fa,default:Na,fetch:Ua},Symbol.toStringTag,{value:"Module"})),qa=t(Ga);var Wa={};Object.defineProperty(Wa,"__esModule",{value:!0});let Ka=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};Wa.default=Ka;var Ja=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty($a,"__esModule",{value:!0});const Va=Ja(qa),Xa=Ja(Wa);$a.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=Va.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,n;let i=null,o=null,s=null,a=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(o="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const n=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),u=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");n&&u&&u.length>1&&(s=parseInt(u[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(o)&&(o.length>1?(i={code:"PGRST116",details:`Results contain ${o.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},o=null,s=null,a=406,c="Not Acceptable"):o=1===o.length?o[0]:null)}else{const t=await e.text();try{i=JSON.parse(t),Array.isArray(i)&&404===e.status&&(o=[],i=null,a=200,c="OK")}catch(u){404===e.status&&""===t?(a=204,c="No Content"):i={message:t}}if(i&&this.isMaybeSingle&&(null===(n=null==i?void 0:i.details)||void 0===n?void 0:n.includes("0 rows"))&&(i=null,a=200,c="OK"),i&&this.shouldThrowOnError)throw new Xa.default(i)}return{error:i,data:o,count:s,status:a,statusText:c}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,n;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(n=null==e?void 0:e.code)&&void 0!==n?n:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}};var Ya=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(La,"__esModule",{value:!0});const Qa=Ya($a);let Za=class extends Qa.default{select(e){let t=!1;const r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:n,referencedTable:i=n}={}){const o=i?`${i}.order`:"order",s=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${s?`${s},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){const n=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(n,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:n=r}={}){const i=void 0===n?"offset":`${n}.offset`,o=void 0===n?"limit":`${n}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(o,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:n=!1,wal:i=!1,format:o="text"}={}){var s;const a=[e?"analyze":null,t?"verbose":null,r?"settings":null,n?"buffers":null,i?"wal":null].filter(Boolean).join("|"),c=null!==(s=this.headers.Accept)&&void 0!==s?s:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${c}"; options=${a};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};La.default=Za;var ec=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ma,"__esModule",{value:!0});const tc=ec(La);let rc=class extends tc.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const r=Array.from(new Set(t)).map(e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:n}={}){let i="";"plain"===n?i="pl":"phrase"===n?i="ph":"websearch"===n&&(i="w");const o=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${i}fts${o}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){const n=r?`${r}.or`:"or";return this.url.searchParams.append(n,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}};Ma.default=rc;var nc=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ia,"__esModule",{value:!0});const ic=nc(Ma);Ia.default=class{constructor(e,{headers:t={},schema:r,fetch:n}){this.url=e,this.headers=t,this.schema=r,this.fetch=n}select(e,{head:t=!1,count:r}={}){const n=t?"HEAD":"GET";let i=!1;const o=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",o),r&&(this.headers.Prefer=`count=${r}`),new ic.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){const n=[];if(this.headers.Prefer&&n.push(this.headers.Prefer),t&&n.push(`count=${t}`),r||n.push("missing=default"),this.headers.Prefer=n.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new ic.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:n,defaultToNull:i=!0}={}){const o=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&o.push(this.headers.Prefer),n&&o.push(`count=${n}`),i||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new ic.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new ic.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new ic.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};var oc={},sc={};Object.defineProperty(sc,"__esModule",{value:!0}),sc.version=void 0,sc.version="0.0.0-automated",Object.defineProperty(oc,"__esModule",{value:!0}),oc.DEFAULT_HEADERS=void 0;const ac=sc;oc.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${ac.version}`};var cc=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ba,"__esModule",{value:!0});const uc=cc(Ia),lc=cc(Ma),hc=oc;Ba.default=class e{constructor(e,{headers:t={},schema:r,fetch:n}={}){this.url=e,this.headers=Object.assign(Object.assign({},hc.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=n}from(e){const t=new URL(`${this.url}/${e}`);return new uc.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new e(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:n=!1,count:i}={}){let o;const s=new URL(`${this.url}/rpc/${e}`);let a;r||n?(o=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{s.searchParams.append(e,t)})):(o="POST",a=t);const c=Object.assign({},this.headers);return i&&(c.Prefer=`count=${i}`),new lc.default({method:o,url:s,headers:c,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}};var dc=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ra,"__esModule",{value:!0}),Ra.PostgrestError=Ra.PostgrestBuilder=Ra.PostgrestTransformBuilder=Ra.PostgrestFilterBuilder=Ra.PostgrestQueryBuilder=Ra.PostgrestClient=void 0;const fc=dc(Ba);Ra.PostgrestClient=fc.default;const pc=dc(Ia);Ra.PostgrestQueryBuilder=pc.default;const gc=dc(Ma);Ra.PostgrestFilterBuilder=gc.default;const vc=dc(La);Ra.PostgrestTransformBuilder=vc.default;const yc=dc($a);Ra.PostgrestBuilder=yc.default;const _c=dc(Wa);Ra.PostgrestError=_c.default;var bc=Ra.default={PostgrestClient:fc.default,PostgrestQueryBuilder:pc.default,PostgrestFilterBuilder:gc.default,PostgrestTransformBuilder:vc.default,PostgrestBuilder:yc.default,PostgrestError:_c.default};const{PostgrestClient:wc,PostgrestQueryBuilder:mc,PostgrestFilterBuilder:kc,PostgrestTransformBuilder:Ec,PostgrestBuilder:Sc,PostgrestError:Oc}=bc;const xc=function(){if("undefined"!=typeof WebSocket)return WebSocket;if(void 0!==global.WebSocket)return global.WebSocket;if(void 0!==window.WebSocket)return window.WebSocket;if(void 0!==self.WebSocket)return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}();var Tc,Pc,jc,Ac,Cc,Rc;!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(Tc||(Tc={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(Pc||(Pc={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(jc||(jc={})),function(e){e.websocket="websocket"}(Ac||(Ac={})),function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(Cc||(Cc={}));class Bc{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){const n=t.getUint8(1),i=t.getUint8(2);let o=this.HEADER_LENGTH+2;const s=r.decode(e.slice(o,o+n));o+=n;const a=r.decode(e.slice(o,o+i));o+=i;return{ref:null,topic:s,event:a,payload:JSON.parse(r.decode(e.slice(o,e.byteLength)))}}}class Ic{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(Rc||(Rc={}));const Mc=(e,t,r={})=>{var n;const i=null!==(n=r.skipTypes)&&void 0!==n?n:[];return Object.keys(t).reduce((r,n)=>(r[n]=Lc(n,e,t,i),r),{})},Lc=(e,t,r,n)=>{const i=t.find(t=>t.name===e),o=null==i?void 0:i.type,s=r[e];return o&&!n.includes(o)?$c(o,s):Dc(s)},$c=(e,t)=>{if("_"===e.charAt(0)){const r=e.slice(1,e.length);return Hc(t,r)}switch(e){case Rc.bool:return Uc(t);case Rc.float4:case Rc.float8:case Rc.int2:case Rc.int4:case Rc.int8:case Rc.numeric:case Rc.oid:return Nc(t);case Rc.json:case Rc.jsonb:return zc(t);case Rc.timestamp:return Fc(t);case Rc.abstime:case Rc.date:case Rc.daterange:case Rc.int4range:case Rc.int8range:case Rc.money:case Rc.reltime:case Rc.text:case Rc.time:case Rc.timestamptz:case Rc.timetz:case Rc.tsrange:case Rc.tstzrange:default:return Dc(t)}},Dc=e=>e,Uc=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Nc=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},zc=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},Hc=(e,t)=>{if("string"!=typeof e)return e;const r=e.length-1,n=e[r];if("{"===e[0]&&"}"===n){let n;const o=e.slice(1,r);try{n=JSON.parse("["+o+"]")}catch(i){n=o?o.split(","):[]}return n.map(e=>$c(t,e))}return e},Fc=e=>"string"==typeof e?e.replace(" ","T"):e,Gc=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class qc{constructor(e,t,r={},n=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=n,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var Wc,Kc,Jc,Vc;!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(Wc||(Wc={}));class Xc{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{const{onJoin:t,onLeave:r,onSync:n}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Xc.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=Xc.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],n()}),this.channel._on(r.diff,{},e=>{const{onJoin:t,onLeave:r,onSync:n}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=Xc.syncDiff(this.state,e,t,r),n())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,n){const i=this.cloneDeep(e),o=this.transformState(t),s={},a={};return this.map(i,(e,t)=>{o[e]||(a[e]=t)}),this.map(o,(e,t)=>{const r=i[e];if(r){const n=t.map(e=>e.presence_ref),i=r.map(e=>e.presence_ref),o=t.filter(e=>i.indexOf(e.presence_ref)<0),c=r.filter(e=>n.indexOf(e.presence_ref)<0);o.length>0&&(s[e]=o),c.length>0&&(a[e]=c)}else s[e]=t}),this.syncDiff(i,{joins:s,leaves:a},r,n)}static syncDiff(e,t,r,n){const{joins:i,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),n||(n=()=>{}),this.map(i,(t,n)=>{var i;const o=null!==(i=e[t])&&void 0!==i?i:[];if(e[t]=this.cloneDeep(n),o.length>0){const r=e[t].map(e=>e.presence_ref),n=o.filter(e=>r.indexOf(e.presence_ref)<0);e[t].unshift(...n)}r(t,o,n)}),this.map(o,(t,r)=>{let i=e[t];if(!i)return;const o=r.map(e=>e.presence_ref);i=i.filter(e=>o.indexOf(e.presence_ref)<0),e[t]=i,n(t,i,r),0===i.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,r)=>{const n=e[r];return t[r]="metas"in n?n.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):n,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(Kc||(Kc={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(Jc||(Jc={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(Vc||(Vc={}));class Yc{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=Pc.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new qc(this,jc.join,this.params,this.timeout),this.rejoinTimer=new Ic(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=Pc.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=Pc.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=Pc.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=Pc.errored,this.rejoinTimer.scheduleTimeout())}),this._on(jc.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new Xc(this),this.broadcastEndpointURL=Gc(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,n;if(this.socket.isConnected()||this.socket.connect(),this.state==Pc.closed){const{config:{broadcast:i,presence:o,private:s}}=this.params;this._onError(t=>null==e?void 0:e(Vc.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(Vc.CLOSED));const a={},c={broadcast:i,presence:o,postgres_changes:null!==(n=null===(r=this.bindings.postgres_changes)||void 0===r?void 0:r.map(e=>e.filter))&&void 0!==n?n:[],private:s};this.socket.accessTokenValue&&(a.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},a)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0!==t){const n=this.bindings.postgres_changes,i=null!==(r=null==n?void 0:n.length)&&void 0!==r?r:0,o=[];for(let r=0;r<i;r++){const i=n[r],{filter:{event:s,schema:a,table:c,filter:u}}=i,l=t&&t[r];if(!l||l.event!==s||l.schema!==a||l.table!==c||l.filter!==u)return this.unsubscribe(),this.state=Pc.errored,void(null==e||e(Vc.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));o.push(Object.assign(Object.assign({},i),{id:l.id}))}return this.bindings.postgres_changes=o,void(e&&e(Vc.SUBSCRIBED))}null==e||e(Vc.SUBSCRIBED)}).receive("error",t=>{this.state=Pc.errored,null==e||e(Vc.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(Vc.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,n;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var n,i,o;const s=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(o=null===(i=null===(n=this.params)||void 0===n?void 0:n.config)||void 0===i?void 0:i.broadcast)||void 0===o?void 0:o.ack)||r("ok"),s.receive("ok",()=>r("ok")),s.receive("error",()=>r("error")),s.receive("timeout",()=>r("timed out"))});{const{event:o,payload:s}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:s,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return await(null===(n=e.body)||void 0===n?void 0:n.cancel()),e.ok?"ok":"error"}catch(i){return"AbortError"===i.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=Pc.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(jc.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(n=>{r=new qc(this,jc.leave,{},e),r.receive("ok",()=>{t(),n("ok")}).receive("timeout",()=>{t(),n("timed out")}).receive("error",()=>{n("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{null==r||r.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){const n=new AbortController,i=setTimeout(()=>n.abort(),r),o=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:n.signal}));return clearTimeout(i),o}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let n=new qc(this,e,t,r);return this._canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var n,i;const o=e.toLocaleLowerCase(),{close:s,error:a,leave:c,join:u}=jc;if(r&&[s,a,c,u].indexOf(o)>=0&&r!==this._joinRef())return;let l=this._onMessage(o,t,r);if(t&&!l)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?null===(n=this.bindings.postgres_changes)||void 0===n||n.filter(e=>{var t,r,n;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(n=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===n?void 0:n.toLocaleLowerCase())===o}).map(e=>e.callback(l,r)):null===(i=this.bindings[o])||void 0===i||i.filter(e=>{var r,n,i,s,a,c;if(["broadcast","presence","postgres_changes"].includes(o)){if("id"in e){const o=e.id,s=null===(r=e.filter)||void 0===r?void 0:r.event;return o&&(null===(n=t.ids)||void 0===n?void 0:n.includes(o))&&("*"===s||(null==s?void 0:s.toLocaleLowerCase())===(null===(i=t.data)||void 0===i?void 0:i.type.toLocaleLowerCase()))}{const r=null===(a=null===(s=null==e?void 0:e.filter)||void 0===s?void 0:s.event)||void 0===a?void 0:a.toLocaleLowerCase();return"*"===r||r===(null===(c=null==t?void 0:t.event)||void 0===c?void 0:c.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===o}).map(e=>{if("object"==typeof l&&"ids"in l){const e=l.data,{schema:t,table:r,commit_timestamp:n,type:i,errors:o}=e,s={schema:t,table:r,commit_timestamp:n,eventType:i,new:{},old:{},errors:o};l=Object.assign(Object.assign({},s),this._getPayloadRecords(e))}e.callback(l,r)})}_isClosed(){return this.state===Pc.closed}_isJoined(){return this.state===Pc.joined}_isJoining(){return this.state===Pc.joining}_isLeaving(){return this.state===Pc.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){const n=e.toLocaleLowerCase(),i={type:n,filter:t,callback:r};return this.bindings[n]?this.bindings[n].push(i):this.bindings[n]=[i],this}_off(e,t){const r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var n;return!((null===(n=e.type)||void 0===n?void 0:n.toLocaleLowerCase())===r&&Yc.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(jc.close,{},e)}_onError(e){this._on(jc.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=Pc.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=Mc(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=Mc(e.columns,e.old_record)),t}}const Qc=()=>{};class Zc{constructor(e,t){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Qc,this.ref=0,this.logger=Qc,this.conn=null,this.sendBuffer=[],this.serializer=new Bc,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Uo(()=>Promise.resolve().then(()=>Ga),void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${Ac.websocket}`,this.httpEndpoint=Gc(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const n=null===(r=null==t?void 0:t.params)||void 0===r?void 0:r.apikey;if(n&&(this.accessTokenValue=n,this.apiKey=n),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Ic(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=xc),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Tc.connecting:return Cc.Connecting;case Tc.open:return Cc.Open;case Tc.closing:return Cc.Closing;default:return Cc.Closed}}isConnected(){return this.connectionState()===Cc.Open}channel(e,t={config:{}}){const r=`realtime:${e}`,n=this.getChannels().find(e=>e.topic===r);if(n)return n;{const r=new Yc(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){const{topic:t,event:r,payload:n,ref:i}=e,o=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${i})`,n),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{const r={access_token:t,version:"realtime-js/2.11.15"};t&&e.updateJoinPayload(r),e.joinedOnce&&e._isJoined()&&e._push(jc.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}else this.heartbeatCallback("disconnected")}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:n,ref:i}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${n.status||""} ${t} ${r} ${i&&"("+i+")"||""}`,n),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,n,i)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(jc.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const r=e.match(/\?/)?"&":"?";return`${e}${r}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class eu extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function tu(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class ru extends eu{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class nu extends eu{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var iu=globalThis&&globalThis.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{c(n.next(e))}catch(t){o(t)}}function a(e){try{c(n.throw(e))}catch(t){o(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(s,a)}c((n=n.apply(e,t||[])).next())})};const ou=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Uo(()=>Promise.resolve().then(()=>Ga),void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},su=e=>{if(Array.isArray(e))return e.map(e=>su(e));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([e,r])=>{const n=e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[n]=su(r)}),t};var au=globalThis&&globalThis.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{c(n.next(e))}catch(t){o(t)}}function a(e){try{c(n.throw(e))}catch(t){o(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(s,a)}c((n=n.apply(e,t||[])).next())})};const cu=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),uu=(e,t,r)=>au(void 0,void 0,void 0,function*(){const n=yield iu(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Uo(()=>Promise.resolve().then(()=>Ga),void 0,import.meta.url)).Response:Response});e instanceof n&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new ru(cu(r),e.status||500))}).catch(e=>{t(new nu(cu(e),e))}):t(new nu(cu(e),e))});function lu(e,t,r,n,i,o){return au(this,void 0,void 0,function*(){return new Promise((s,a)=>{e(r,((e,t,r,n)=>{const i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),n&&(i.body=JSON.stringify(n)),Object.assign(Object.assign({},i),r))})(t,n,i,o)).then(e=>{if(!e.ok)throw e;return(null==n?void 0:n.noResolveJson)?e:e.json()}).then(e=>s(e)).catch(e=>uu(e,a,n))})})}function hu(e,t,r,n){return au(this,void 0,void 0,function*(){return lu(e,"GET",t,r,n)})}function du(e,t,r,n,i){return au(this,void 0,void 0,function*(){return lu(e,"POST",t,n,i,r)})}function fu(e,t,r,n,i){return au(this,void 0,void 0,function*(){return lu(e,"DELETE",t,n,i,r)})}var pu=globalThis&&globalThis.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{c(n.next(e))}catch(t){o(t)}}function a(e){try{c(n.throw(e))}catch(t){o(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(s,a)}c((n=n.apply(e,t||[])).next())})};const gu={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},vu={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class yu{constructor(e,t={},r,n){this.url=e,this.headers=t,this.bucketId=r,this.fetch=ou(n)}uploadOrUpdate(e,t,r,n){return pu(this,void 0,void 0,function*(){try{let i;const o=Object.assign(Object.assign({},vu),n);let s=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(o.upsert)});const a=o.metadata;"undefined"!=typeof Blob&&r instanceof Blob?(i=new FormData,i.append("cacheControl",o.cacheControl),a&&i.append("metadata",this.encodeMetadata(a)),i.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(i=r,i.append("cacheControl",o.cacheControl),a&&i.append("metadata",this.encodeMetadata(a))):(i=r,s["cache-control"]=`max-age=${o.cacheControl}`,s["content-type"]=o.contentType,a&&(s["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==n?void 0:n.headers)&&(s=Object.assign(Object.assign({},s),n.headers));const c=this._removeEmptyFolders(t),u=this._getFinalPath(c),l=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:i,headers:s},(null==o?void 0:o.duplex)?{duplex:o.duplex}:{})),h=yield l.json();if(l.ok)return{data:{path:c,id:h.Id,fullPath:h.Key},error:null};return{data:null,error:h}}catch(i){if(tu(i))return{data:null,error:i};throw i}})}upload(e,t,r){return pu(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,n){return pu(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(e),o=this._getFinalPath(i),s=new URL(this.url+`/object/upload/sign/${o}`);s.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:vu.upsert},n),o=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r,e.append("cacheControl",t.cacheControl)):(e=r,o["cache-control"]=`max-age=${t.cacheControl}`,o["content-type"]=t.contentType);const a=yield this.fetch(s.toString(),{method:"PUT",body:e,headers:o}),c=yield a.json();if(a.ok)return{data:{path:i,fullPath:c.Key},error:null};return{data:null,error:c}}catch(a){if(tu(a))return{data:null,error:a};throw a}})}createSignedUploadUrl(e,t){return pu(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e);const n=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(n["x-upsert"]="true");const i=yield du(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:n}),o=new URL(this.url+i.url),s=o.searchParams.get("token");if(!s)throw new eu("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:s},error:null}}catch(r){if(tu(r))return{data:null,error:r};throw r}})}update(e,t,r){return pu(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return pu(this,void 0,void 0,function*(){try{return{data:yield du(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(n){if(tu(n))return{data:null,error:n};throw n}})}copy(e,t,r){return pu(this,void 0,void 0,function*(){try{return{data:{path:(yield du(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(n){if(tu(n))return{data:null,error:n};throw n}})}createSignedUrl(e,t,r){return pu(this,void 0,void 0,function*(){try{let n=this._getFinalPath(e),i=yield du(this.fetch,`${this.url}/object/sign/${n}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers});const o=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${o}`)},{data:i,error:null}}catch(n){if(tu(n))return{data:null,error:n};throw n}})}createSignedUrls(e,t,r){return pu(this,void 0,void 0,function*(){try{const n=yield du(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:n.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${i}`):null})),error:null}}catch(n){if(tu(n))return{data:null,error:n};throw n}})}download(e,t){return pu(this,void 0,void 0,function*(){const r=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",n=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),i=n?`?${n}`:"";try{const t=this._getFinalPath(e),n=yield hu(this.fetch,`${this.url}/${r}/${t}${i}`,{headers:this.headers,noResolveJson:!0});return{data:yield n.blob(),error:null}}catch(o){if(tu(o))return{data:null,error:o};throw o}})}info(e){return pu(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const e=yield hu(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:su(e),error:null}}catch(r){if(tu(r))return{data:null,error:r};throw r}})}exists(e){return pu(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield function(e,t,r,n){return au(this,void 0,void 0,function*(){return lu(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),n)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(tu(r)&&r instanceof nu){const e=r.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:r}}throw r}})}getPublicUrl(e,t){const r=this._getFinalPath(e),n=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&n.push(i);const o=void 0!==(null==t?void 0:t.transform)?"render/image":"object",s=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==s&&n.push(s);let a=n.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${o}/public/${r}${a}`)}}}remove(e){return pu(this,void 0,void 0,function*(){try{return{data:yield fu(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(tu(t))return{data:null,error:t};throw t}})}list(e,t,r){return pu(this,void 0,void 0,function*(){try{const n=Object.assign(Object.assign(Object.assign({},gu),t),{prefix:e||""});return{data:yield du(this.fetch,`${this.url}/object/list/${this.bucketId}`,n,{headers:this.headers},r),error:null}}catch(n){if(tu(n))return{data:null,error:n};throw n}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const _u={"X-Client-Info":"storage-js/2.7.1"};var bu=globalThis&&globalThis.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{c(n.next(e))}catch(t){o(t)}}function a(e){try{c(n.throw(e))}catch(t){o(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(s,a)}c((n=n.apply(e,t||[])).next())})};class wu{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},_u),t),this.fetch=ou(r)}listBuckets(){return bu(this,void 0,void 0,function*(){try{return{data:yield hu(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(tu(e))return{data:null,error:e};throw e}})}getBucket(e){return bu(this,void 0,void 0,function*(){try{return{data:yield hu(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(tu(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return bu(this,void 0,void 0,function*(){try{return{data:yield du(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(tu(r))return{data:null,error:r};throw r}})}updateBucket(e,t){return bu(this,void 0,void 0,function*(){try{const r=yield function(e,t,r,n,i){return au(this,void 0,void 0,function*(){return lu(e,"PUT",t,n,i,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:r,error:null}}catch(r){if(tu(r))return{data:null,error:r};throw r}})}emptyBucket(e){return bu(this,void 0,void 0,function*(){try{return{data:yield du(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(tu(t))return{data:null,error:t};throw t}})}deleteBucket(e){return bu(this,void 0,void 0,function*(){try{return{data:yield fu(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(tu(t))return{data:null,error:t};throw t}})}}class mu extends wu{constructor(e,t={},r){super(e,t,r)}from(e){return new yu(this.url,this.headers,e,this.fetch)}}let ku="";ku="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const Eu={headers:{"X-Client-Info":`supabase-js-${ku}/2.50.5`}},Su={schema:"public"},Ou={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},xu={};var Tu=globalThis&&globalThis.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{c(n.next(e))}catch(t){o(t)}}function a(e){try{c(n.throw(e))}catch(t){o(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(s,a)}c((n=n.apply(e,t||[])).next())})};const Pu=e=>{let t;return t=e||("undefined"==typeof fetch?Na:fetch),(...e)=>t(...e)},ju=(e,t,r)=>{const n=Pu(r),i="undefined"==typeof Headers?za:Headers;return(r,o)=>Tu(void 0,void 0,void 0,function*(){var s;const a=null!==(s=yield t())&&void 0!==s?s:e;let c=new i(null==o?void 0:o.headers);return c.has("apikey")||c.set("apikey",e),c.has("Authorization")||c.set("Authorization",`Bearer ${a}`),n(r,Object.assign(Object.assign({},o),{headers:c}))})};var Au=globalThis&&globalThis.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{c(n.next(e))}catch(t){o(t)}}function a(e){try{c(n.throw(e))}catch(t){o(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(s,a)}c((n=n.apply(e,t||[])).next())})};const Cu="2.70.0",Ru=3e4,Bu=9e4,Iu={"X-Client-Info":`gotrue-js/${Cu}`},Mu="X-Supabase-Api-Version",Lu={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},$u=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class Du extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function Uu(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class Nu extends Du{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class zu extends Du{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class Hu extends Du{constructor(e,t,r,n){super(e,r,n),this.name=t,this.status=r}}class Fu extends Hu{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class Gu extends Hu{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class qu extends Hu{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class Wu extends Hu{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Ku extends Hu{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Ju extends Hu{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function Vu(e){return Uu(e)&&"AuthRetryableFetchError"===e.name}class Xu extends Hu{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class Yu extends Hu{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const Qu="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Zu=" \t\n\r=".split(""),el=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Zu.length;t+=1)e[Zu[t].charCodeAt(0)]=-2;for(let t=0;t<Qu.length;t+=1)e[Qu[t].charCodeAt(0)]=t;return e})();function tl(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;r(Qu[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;r(Qu[e]),t.queuedBits-=6}}function rl(e,t,r){const n=el[e];if(!(n>-1)){if(-2===n)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|n,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function nl(e){const t=[],r=e=>{t.push(String.fromCodePoint(e))},n={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},o=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return void r(e);for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,n,r)};for(let s=0;s<e.length;s+=1)rl(e.charCodeAt(s),i,o);return t.join("")}function il(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function ol(e){const t=[],r={queue:0,queuedBits:0},n=e=>{t.push(e)};for(let i=0;i<e.length;i+=1)rl(e.charCodeAt(i),r,n);return new Uint8Array(t)}function sl(e){const t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let n=e.charCodeAt(r);if(n>55295&&n<=56319){const t=1024*(n-55296)&65535;n=65536+(e.charCodeAt(r+1)-56320&65535|t),r+=1}il(n,t)}}(e,e=>t.push(e)),new Uint8Array(t)}function al(e){const t=[],r={queue:0,queuedBits:0},n=e=>{t.push(e)};return e.forEach(e=>tl(e,r,n)),tl(null,r,n),t.join("")}const cl=()=>"undefined"!=typeof window&&"undefined"!=typeof document,ul={tested:!1,writable:!1},ll=()=>{if(!cl())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(t){return!1}if(ul.tested)return ul.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),ul.tested=!0,ul.writable=!0}catch(t){ul.tested=!0,ul.writable=!1}return ul.writable};const hl=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Uo(()=>Promise.resolve().then(()=>Ga),void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},dl=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},fl=async(e,t)=>{const r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(n){return r}},pl=async(e,t)=>{await e.removeItem(t)};class gl{constructor(){this.promise=new gl.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function vl(e){const t=e.split(".");if(3!==t.length)throw new Yu("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!$u.test(t[r]))throw new Yu("JWT not in base64url format");return{header:JSON.parse(nl(t[0])),payload:JSON.parse(nl(t[1])),signature:ol(t[2]),raw:{header:t[0],payload:t[1]}}}function yl(e){return("0"+e.toString(16)).substr(-2)}async function _l(e){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const t=await async function(e){const t=(new TextEncoder).encode(e),r=await crypto.subtle.digest("SHA-256",t),n=new Uint8Array(r);return Array.from(n).map(e=>String.fromCharCode(e)).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function bl(e,t,r=!1){const n=function(){const e=new Uint32Array(56);if("undefined"==typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let r="";for(let n=0;n<56;n++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,yl).join("")}();let i=n;r&&(i+="/PASSWORD_RECOVERY"),await dl(e,`${t}-code-verifier`,i);const o=await _l(n);return[o,n===o?"plain":"s256"]}gl.promiseConstructor=Promise;const wl=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const ml=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function kl(e){if(!ml.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var El=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r};const Sl=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Ol=[502,503,504];async function xl(e){var t,r;if(!("object"==typeof(r=e)&&null!==r&&"status"in r&&"ok"in r&&"json"in r&&"function"==typeof r.json))throw new Ju(Sl(e),0);if(Ol.includes(e.status))throw new Ju(Sl(e),e.status);let n,i;try{n=await e.json()}catch(s){throw new zu(Sl(s),s)}const o=function(e){const t=e.headers.get(Mu);if(!t)return null;if(!t.match(wl))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(s){return null}}(e);if(o&&o.getTime()>=Lu.timestamp&&"object"==typeof n&&n&&"string"==typeof n.code?i=n.code:"object"==typeof n&&n&&"string"==typeof n.error_code&&(i=n.error_code),i){if("weak_password"===i)throw new Xu(Sl(n),e.status,(null===(t=n.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===i)throw new Fu}else if("object"==typeof n&&n&&"object"==typeof n.weak_password&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new Xu(Sl(n),e.status,n.weak_password.reasons);throw new Nu(Sl(n),e.status||500,i)}async function Tl(e,t,r,n){var i;const o=Object.assign({},null==n?void 0:n.headers);o[Mu]||(o[Mu]=Lu.name),(null==n?void 0:n.jwt)&&(o.Authorization=`Bearer ${n.jwt}`);const s=null!==(i=null==n?void 0:n.query)&&void 0!==i?i:{};(null==n?void 0:n.redirectTo)&&(s.redirect_to=n.redirectTo);const a=Object.keys(s).length?"?"+new URLSearchParams(s).toString():"",c=await async function(e,t,r,n,i,o){const s=((e,t,r,n)=>{const i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(n),Object.assign(Object.assign({},i),r))})(t,n,i,o);let a;try{a=await e(r,Object.assign({},s))}catch(c){throw console.error(c),new Ju(Sl(c),0)}a.ok||await xl(a);if(null==n?void 0:n.noResolveJson)return a;try{return await a.json()}catch(c){await xl(c)}}(e,t,r+a,{headers:o,noResolveJson:null==n?void 0:n.noResolveJson},{},null==n?void 0:n.body);return(null==n?void 0:n.xform)?null==n?void 0:n.xform(c):{data:Object.assign({},c),error:null}}function Pl(e){var t;let r=null;var n;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(r=Object.assign({},e),e.expires_at||(r.expires_at=(n=e.expires_in,Math.round(Date.now()/1e3)+n)));return{data:{session:r,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function jl(e){const t=Pl(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function Al(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Cl(e){return{data:e,error:null}}function Rl(e){const{action_link:t,email_otp:r,hashed_token:n,redirect_to:i,verification_type:o}=e,s=El(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:r,hashed_token:n,redirect_to:i,verification_type:o},user:Object.assign({},s)},error:null}}function Bl(e){return e}const Il=["global","local","others"];var Ml=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r};class Ll{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=hl(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=Il[0]){if(Il.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Il.join(", ")}`);try{return await Tl(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(r){if(Uu(r))return{data:null,error:r};throw r}}async inviteUserByEmail(e,t={}){try{return await Tl(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:Al})}catch(r){if(Uu(r))return{data:{user:null},error:r};throw r}}async generateLink(e){try{const{options:t}=e,r=Ml(e,["options"]),n=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(n.new_email=null==r?void 0:r.newEmail,delete n.newEmail),await Tl(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:n,headers:this.headers,xform:Rl,redirectTo:null==t?void 0:t.redirectTo})}catch(t){if(Uu(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await Tl(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:Al})}catch(t){if(Uu(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,r,n,i,o,s,a;try{const c={nextPage:null,lastPage:0,total:0},u=await Tl(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==r?r:"",per_page:null!==(i=null===(n=null==e?void 0:e.perPage)||void 0===n?void 0:n.toString())&&void 0!==i?i:""},xform:Bl});if(u.error)throw u.error;const l=await u.json(),h=null!==(o=u.headers.get("x-total-count"))&&void 0!==o?o:0,d=null!==(a=null===(s=u.headers.get("link"))||void 0===s?void 0:s.split(","))&&void 0!==a?a:[];return d.length>0&&(d.forEach(e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);c[`${r}Page`]=t}),c.total=parseInt(h)),{data:Object.assign(Object.assign({},l),c),error:null}}catch(c){if(Uu(c))return{data:{users:[]},error:c};throw c}}async getUserById(e){kl(e);try{return await Tl(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:Al})}catch(t){if(Uu(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){kl(e);try{return await Tl(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:Al})}catch(r){if(Uu(r))return{data:{user:null},error:r};throw r}}async deleteUser(e,t=!1){kl(e);try{return await Tl(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:Al})}catch(r){if(Uu(r))return{data:{user:null},error:r};throw r}}async _listFactors(e){kl(e.userId);try{const{data:t,error:r}=await Tl(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(t){if(Uu(t))return{data:null,error:t};throw t}}async _deleteFactor(e){kl(e.userId),kl(e.id);try{return{data:await Tl(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(Uu(t))return{data:null,error:t};throw t}}}const $l={getItem:e=>ll()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{ll()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{ll()&&globalThis.localStorage.removeItem(e)}};function Dl(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}const Ul=!!(globalThis&&ll()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class Nl extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class zl extends Nl{}async function Hl(e,t,r){Ul&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const n=new globalThis.AbortController;return t>0&&setTimeout(()=>{n.abort(),Ul&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:n.signal},async n=>{if(!n){if(0===t)throw Ul&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new zl(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(Ul)try{const e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(i){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",i)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}Ul&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,n.name);try{return await r()}finally{Ul&&console.log("@supabase/gotrue-js: navigatorLock: released",e,n.name)}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const Fl={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Iu,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function Gl(e,t,r){return await r()}class ql{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=ql.nextInstanceID,ql.nextInstanceID+=1,this.instanceID>0&&cl()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const n=Object.assign(Object.assign({},Fl),e);if(this.logDebugMessages=!!n.debug,"function"==typeof n.debug&&(this.logger=n.debug),this.persistSession=n.persistSession,this.storageKey=n.storageKey,this.autoRefreshToken=n.autoRefreshToken,this.admin=new Ll({url:n.url,headers:n.headers,fetch:n.fetch}),this.url=n.url,this.headers=n.headers,this.fetch=hl(n.fetch),this.lock=n.lock||Gl,this.detectSessionInUrl=n.detectSessionInUrl,this.flowType=n.flowType,this.hasCustomAuthorizationHeader=n.hasCustomAuthorizationHeader,n.lock?this.lock=n.lock:cl()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=Hl:this.lock=Gl,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?n.storage?this.storage=n.storage:ll()?this.storage=$l:(this.memoryStorage={},this.storage=Dl(this.memoryStorage)):(this.memoryStorage={},this.storage=Dl(this.memoryStorage)),cl()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",i)}null===(r=this.broadcastChannel)||void 0===r||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Cu}) ${(new Date).toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(n){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href);let r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),cl()&&this.detectSessionInUrl&&"none"!==r){const{data:n,error:i}=await this._getSessionFromURL(t,r);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),function(e){return Uu(e)&&"AuthImplicitGrantRedirectError"===e.name}(i)){const t=null===(e=i.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:i}}return await this._removeSession(),{error:i}}const{session:o,redirectType:s}=n;return this._debug("#_initialize()","detected session in URL",o,"redirect type",s),await this._saveSession(o),setTimeout(async()=>{"recovery"===s?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return Uu(t)?{error:t}:{error:new zu("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,n;try{const i=await Tl(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(n=null==e?void 0:e.options)||void 0===n?void 0:n.captchaToken}},xform:Pl}),{data:o,error:s}=i;if(s||!o)return{data:{user:null,session:null},error:s};const a=o.session,c=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(i){if(Uu(i))return{data:{user:null,session:null},error:i};throw i}}async signUp(e){var t,r,n;try{let i;if("email"in e){const{email:r,password:n,options:o}=e;let s=null,a=null;"pkce"===this.flowType&&([s,a]=await bl(this.storage,this.storageKey)),i=await Tl(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==o?void 0:o.emailRedirectTo,body:{email:r,password:n,data:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken},code_challenge:s,code_challenge_method:a},xform:Pl})}else{if(!("phone"in e))throw new qu("You must provide either an email or phone number and a password");{const{phone:t,password:o,options:s}=e;i=await Tl(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:o,data:null!==(r=null==s?void 0:s.data)&&void 0!==r?r:{},channel:null!==(n=null==s?void 0:s.channel)&&void 0!==n?n:"sms",gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:Pl})}}const{data:o,error:s}=i;if(s||!o)return{data:{user:null,session:null},error:s};const a=o.session,c=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(i){if(Uu(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithPassword(e){try{let t;if("email"in e){const{email:r,password:n,options:i}=e;t=await Tl(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:jl})}else{if(!("phone"in e))throw new qu("You must provide either an email or phone number and a password");{const{phone:r,password:n,options:i}=e;t=await Tl(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:jl})}}const{data:r,error:n}=t;return n?{data:{user:null,session:null},error:n}:r&&r.session&&r.user?(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:n}):{data:{user:null,session:null},error:new Gu}}catch(t){if(Uu(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,r,n,i;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(n=e.options)||void 0===n?void 0:n.queryParams,skipBrowserRedirect:null===(i=e.options)||void 0===i?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){const{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,n,i,o,s,a,c,u,l,h,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{const{chain:h,wallet:d,statement:g,options:v}=e;let y;if(cl())if("object"==typeof d)y=d;else{const e=window;if(!("solana"in e)||"object"!=typeof e.solana||!("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");y=e.solana}else{if("object"!=typeof d||!(null==v?void 0:v.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");y=d}const _=new URL(null!==(t=null==v?void 0:v.url)&&void 0!==t?t:window.location.href);if("signIn"in y&&y.signIn){const e=await y.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null==v?void 0:v.signInWithSolana),{version:"1",domain:_.host,uri:_.href}),g?{statement:g}:null));let t;if(Array.isArray(e)&&e[0]&&"object"==typeof e[0])t=e[0];else{if(!(e&&"object"==typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"==typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");f="string"==typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),p=t.signature}else{if(!("signMessage"in y&&"function"==typeof y.signMessage&&"publicKey"in y&&"object"==typeof y&&y.publicKey&&"toBase58"in y.publicKey&&"function"==typeof y.publicKey.toBase58))throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${_.host} wants you to sign in with your Solana account:`,y.publicKey.toBase58(),...g?["",g,""]:[""],"Version: 1",`URI: ${_.href}`,`Issued At: ${null!==(n=null===(r=null==v?void 0:v.signInWithSolana)||void 0===r?void 0:r.issuedAt)&&void 0!==n?n:(new Date).toISOString()}`,...(null===(i=null==v?void 0:v.signInWithSolana)||void 0===i?void 0:i.notBefore)?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...(null===(o=null==v?void 0:v.signInWithSolana)||void 0===o?void 0:o.expirationTime)?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...(null===(s=null==v?void 0:v.signInWithSolana)||void 0===s?void 0:s.chainId)?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...(null===(a=null==v?void 0:v.signInWithSolana)||void 0===a?void 0:a.nonce)?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...(null===(c=null==v?void 0:v.signInWithSolana)||void 0===c?void 0:c.requestId)?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...(null===(l=null===(u=null==v?void 0:v.signInWithSolana)||void 0===u?void 0:u.resources)||void 0===l?void 0:l.length)?["Resources",...v.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");const e=await y.signMessage((new TextEncoder).encode(f),"utf8");if(!(e&&e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{const{data:t,error:r}=await Tl(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:al(p)},(null===(h=e.options)||void 0===h?void 0:h.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:Pl});if(r)throw r;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}):{data:{user:null,session:null},error:new Gu}}catch(g){if(Uu(g))return{data:{user:null,session:null},error:g};throw g}}async _exchangeCodeForSession(e){const t=await fl(this.storage,`${this.storageKey}-code-verifier`),[r,n]=(null!=t?t:"").split("/");try{const{data:t,error:i}=await Tl(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:Pl});if(await pl(this.storage,`${this.storageKey}-code-verifier`),i)throw i;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=n?n:null}),error:i}):{data:{user:null,session:null,redirectType:null},error:new Gu}}catch(i){if(Uu(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}}async signInWithIdToken(e){try{const{options:t,provider:r,token:n,access_token:i,nonce:o}=e,s=await Tl(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:n,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:Pl}),{data:a,error:c}=s;return c?{data:{user:null,session:null},error:c}:a&&a.session&&a.user?(a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:c}):{data:{user:null,session:null},error:new Gu}}catch(t){if(Uu(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,r,n,i,o;try{if("email"in e){const{email:n,options:i}=e;let o=null,s=null;"pkce"===this.flowType&&([o,s]=await bl(this.storage,this.storageKey));const{error:a}=await Tl(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:n,data:null!==(t=null==i?void 0:i.data)&&void 0!==t?t:{},create_user:null===(r=null==i?void 0:i.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:o,code_challenge_method:s},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in e){const{phone:t,options:r}=e,{data:s,error:a}=await Tl(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(n=null==r?void 0:r.data)&&void 0!==n?n:{},create_user:null===(i=null==r?void 0:r.shouldCreateUser)||void 0===i||i,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(o=null==r?void 0:r.channel)&&void 0!==o?o:"sms"}});return{data:{user:null,session:null,messageId:null==s?void 0:s.message_id},error:a}}throw new qu("You must provide either an email or phone number.")}catch(s){if(Uu(s))return{data:{user:null,session:null},error:s};throw s}}async verifyOtp(e){var t,r;try{let n,i;"options"in e&&(n=null===(t=e.options)||void 0===t?void 0:t.redirectTo,i=null===(r=e.options)||void 0===r?void 0:r.captchaToken);const{data:o,error:s}=await Tl(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:n,xform:Pl});if(s)throw s;if(!o)throw new Error("An error occurred on token verification.");const a=o.session,c=o.user;return(null==a?void 0:a.access_token)&&(await this._saveSession(a),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(n){if(Uu(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithSSO(e){var t,r,n;try{let i=null,o=null;return"pkce"===this.flowType&&([i,o]=await bl(this.storage,this.storageKey)),await Tl(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==r?r:void 0}),(null===(n=null==e?void 0:e.options)||void 0===n?void 0:n.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:o}),headers:this.headers,xform:Cl})}catch(i){if(Uu(i))return{data:null,error:i};throw i}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new Fu;const{error:n}=await Tl(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:n}})}catch(e){if(Uu(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:r,type:n,options:i}=e,{error:o}=await Tl(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){const{phone:r,type:n,options:i}=e,{data:o,error:s}=await Tl(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:s}}throw new qu("You must provide either an email or phone number and a type")}catch(t){if(Uu(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(t){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await fl(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const r=!!e.expires_at&&1e3*e.expires_at-Date.now()<Bu;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,n)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,n))})}return{data:{session:e},error:null}}const{session:n,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{session:null},error:i}:{data:{session:n},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,async()=>await this._getUser())}async _getUser(e){try{return e?await Tl(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:Al}):await this._useSession(async e=>{var t,r,n;const{data:i,error:o}=e;if(o)throw o;return(null===(t=i.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await Tl(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(n=null===(r=i.session)||void 0===r?void 0:r.access_token)&&void 0!==n?n:void 0,xform:Al}):{data:{user:null},error:new Fu}})}catch(t){if(Uu(t))return function(e){return Uu(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await pl(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{const{data:n,error:i}=r;if(i)throw i;if(!n.session)throw new Fu;const o=n.session;let s=null,a=null;"pkce"===this.flowType&&null!=e.email&&([s,a]=await bl(this.storage,this.storageKey));const{data:c,error:u}=await Tl(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:s,code_challenge_method:a}),jwt:o.access_token,xform:Al});if(u)throw u;return o.user=c.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(r){if(Uu(r))return{data:{user:null},error:r};throw r}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new Fu;const t=Date.now()/1e3;let r=t,n=!0,i=null;const{payload:o}=vl(e.access_token);if(o.exp&&(r=o.exp,n=r<=t),n){const{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};i=t}else{const{data:n,error:o}=await this._getUser(e.access_token);if(o)throw o;i={access_token:e.access_token,refresh_token:e.refresh_token,user:n.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(t){if(Uu(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){const{data:n,error:i}=t;if(i)throw i;e=null!==(r=n.session)&&void 0!==r?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new Fu;const{session:n,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:n?{data:{user:n.user,session:n},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(Uu(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!cl())throw new Wu("No browser detected.");if(e.error||e.error_description||e.error_code)throw new Wu(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new Ku("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new Wu("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new Ku("No code detected.");const{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;const n=new URL(window.location.href);return n.searchParams.delete("code"),window.history.replaceState(window.history.state,"",n.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:n,access_token:i,refresh_token:o,expires_in:s,expires_at:a,token_type:c}=e;if(!(i&&s&&o&&c))throw new Wu("No session defined in URL");const u=Math.round(Date.now()/1e3),l=parseInt(s);let h=u+l;a&&(h=parseInt(a));const d=h-u;1e3*d<=Ru&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${l}s`);const f=h-l;u-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,h,u):u-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,h,u);const{data:p,error:g}=await this._getUser(i);if(g)throw g;const v={provider_token:r,provider_refresh_token:n,access_token:i,expires_in:l,expires_at:h,refresh_token:o,token_type:c,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:e.type},error:null}}catch(r){if(Uu(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await fl(this.storage,`${this.storageKey}-code-verifier`);return!(!e.code||!t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;const{data:n,error:i}=t;if(i)return{error:i};const o=null===(r=n.session)||void 0===r?void 0:r.access_token;if(o){const{error:t}=await this.admin.signOut(o,e);if(t&&(!function(e){return Uu(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await pl(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,n;try{const{data:{session:n},error:i}=t;if(i)throw i;await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",n)),this._debug("INITIAL_SESSION","callback id",e,"session",n)}catch(i){await(null===(n=this.stateChangeEmitters.get(e))||void 0===n?void 0:n.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",i),console.error(i)}})}async resetPasswordForEmail(e,t={}){let r=null,n=null;"pkce"===this.flowType&&([r,n]=await bl(this.storage,this.storageKey,!0));try{return await Tl(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:n,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(i){if(Uu(i))return{data:null,error:i};throw i}}async getUserIdentities(){var e;try{const{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(Uu(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:r,error:n}=await this._useSession(async t=>{var r,n,i,o,s;const{data:a,error:c}=t;if(c)throw c;const u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(r=e.options)||void 0===r?void 0:r.redirectTo,scopes:null===(n=e.options)||void 0===n?void 0:n.scopes,queryParams:null===(i=e.options)||void 0===i?void 0:i.queryParams,skipBrowserRedirect:!0});return await Tl(this.fetch,"GET",u,{headers:this.headers,jwt:null!==(s=null===(o=a.session)||void 0===o?void 0:o.access_token)&&void 0!==s?s:void 0})});if(n)throw n;return cl()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(r){if(Uu(r))return{data:{provider:e.provider,url:null},error:r};throw r}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,n;const{data:i,error:o}=t;if(o)throw o;return await Tl(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(n=null===(r=i.session)||void 0===r?void 0:r.access_token)&&void 0!==n?n:void 0})})}catch(t){if(Uu(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const i=Date.now();return await(r=async r=>(r>0&&await async function(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await Tl(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:Pl})),n=(e,t)=>{const r=200*Math.pow(2,e);return t&&Vu(t)&&Date.now()+r-i<Ru},new Promise((e,t)=>{(async()=>{for(let o=0;o<1/0;o++)try{const t=await r(o);if(!n(o,null,t))return void e(t)}catch(i){if(!n(o,i))return void t(i)}})()}))}catch(i){if(this._debug(t,"error",i),Uu(i))return{data:{session:null,user:null},error:i};throw i}finally{this._debug(t,"end")}var r,n}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),cl()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const r=await fl(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r))return this._debug(t,"session is not valid"),void(null!==r&&await this._removeSession());const n=1e3*(null!==(e=r.expires_at)&&void 0!==e?e:1/0)-Date.now()<Bu;if(this._debug(t,`session has${n?"":" not"} expired with margin of 90000s`),n){if(this.autoRefreshToken&&r.refresh_token){const{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),Vu(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){return this._debug(t,"error",r),void console.error(r)}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new Fu;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const n=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(n,"begin");try{this.refreshingDeferred=new gl;const{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new Fu;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const n={session:t.session,error:null};return this.refreshingDeferred.resolve(n),n}catch(i){if(this._debug(n,"error",i),Uu(i)){const e={session:null,error:i};return Vu(i)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(i),i}finally{this.refreshingDeferred=null,this._debug(n,"end")}}async _notifyAllSubscribers(e,t,r=!0){const n=`#_notifyAllSubscribers(${e})`;this._debug(n,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});const n=[],i=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(i){n.push(i)}});if(await Promise.all(i),n.length>0){for(let e=0;e<n.length;e+=1)console.error(n[e]);throw n[0]}}finally{this._debug(n,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await dl(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await pl(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&cl()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),Ru);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async e=>{const{data:{session:r}}=e;if(!r||!r.refresh_token||!r.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const n=Math.floor((1e3*r.expires_at-t)/Ru);this._debug("#_autoRefreshTokenTick()",`access token expires in ${n} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),n<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(!(e.isAcquireTimeout||e instanceof Nl))throw e;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!cl()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){const n=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&n.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&n.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){const[e,t]=await bl(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});n.push(r.toString())}if(null==r?void 0:r.queryParams){const e=new URLSearchParams(r.queryParams);n.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&n.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${n.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;const{data:n,error:i}=t;return i?{data:null,error:i}:await Tl(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(r=null==n?void 0:n.session)||void 0===r?void 0:r.access_token})})}catch(t){if(Uu(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var r,n;const{data:i,error:o}=t;if(o)return{data:null,error:o};const s=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:a,error:c}=await Tl(this.fetch,"POST",`${this.url}/factors`,{body:s,headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token});return c?{data:null,error:c}:("totp"===e.factorType&&(null===(n=null==a?void 0:a.totp)||void 0===n?void 0:n.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})})}catch(t){if(Uu(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;const{data:n,error:i}=t;if(i)return{data:null,error:i};const{data:o,error:s}=await Tl(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(r=null==n?void 0:n.session)||void 0===r?void 0:r.access_token});return s?{data:null,error:s}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:s})})}catch(t){if(Uu(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;const{data:n,error:i}=t;return i?{data:null,error:i}:await Tl(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(r=null==n?void 0:n.session)||void 0===r?void 0:r.access_token})})}catch(t){if(Uu(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const r=(null==e?void 0:e.factors)||[],n=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),i=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:n,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;const{data:{session:n},error:i}=e;if(i)return{data:null,error:i};if(!n)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=vl(n.access_token);let s=null;o.aal&&(s=o.aal);let a=s;(null!==(r=null===(t=n.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(a="aal2");return{data:{currentLevel:s,nextLevel:a,currentAuthenticationMethods:o.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r)return r;if(r=this.jwks.keys.find(t=>t.kid===e),r&&this.jwks_cached_at+6e5>Date.now())return r;const{data:n,error:i}=await Tl(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!n.keys||0===n.keys.length)throw new Yu("JWKS is empty");if(this.jwks=n,this.jwks_cached_at=Date.now(),r=n.keys.find(t=>t.kid===e),!r)throw new Yu("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}const{header:n,payload:i,signature:o,raw:{header:s,payload:a}}=vl(r);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(i.exp),!n.kid||"HS256"===n.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:i,header:n,signature:o},error:null}}const c=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(n.alg),u=await this.fetchJwk(n.kid,t),l=await crypto.subtle.importKey("jwk",u,c,!0,["verify"]);if(!(await crypto.subtle.verify(c,l,o,sl(`${s}.${a}`))))throw new Yu("Invalid JWT signature");return{data:{claims:i,header:n,signature:o},error:null}}catch(r){if(Uu(r))return{data:null,error:r};throw r}}}ql.nextInstanceID=0;const Wl=ql;class Kl extends Wl{constructor(e){super(e)}}var Jl=globalThis&&globalThis.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{c(n.next(e))}catch(t){o(t)}}function a(e){try{c(n.throw(e))}catch(t){o(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(s,a)}c((n=n.apply(e,t||[])).next())})};class Vl{constructor(e,t,r){var n,i,o;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const s=(a=e).endsWith("/")?a:a+"/";var a;const c=new URL(s);this.realtimeUrl=new URL("realtime/v1",c),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",c),this.storageUrl=new URL("storage/v1",c),this.functionsUrl=new URL("functions/v1",c);const u=`sb-${c.hostname.split(".")[0]}-auth-token`,l=function(e,t){var r,n;const{db:i,auth:o,realtime:s,global:a}=e,{db:c,auth:u,realtime:l,global:h}=t,d={db:Object.assign(Object.assign({},c),i),auth:Object.assign(Object.assign({},u),o),realtime:Object.assign(Object.assign({},l),s),global:Object.assign(Object.assign(Object.assign({},h),a),{headers:Object.assign(Object.assign({},null!==(r=null==h?void 0:h.headers)&&void 0!==r?r:{}),null!==(n=null==a?void 0:a.headers)&&void 0!==n?n:{})}),accessToken:()=>Au(this,void 0,void 0,function*(){return""})};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:Su,realtime:xu,auth:Object.assign(Object.assign({},Ou),{storageKey:u}),global:Eu});this.storageKey=null!==(n=l.auth.storageKey)&&void 0!==n?n:"",this.headers=null!==(i=l.global.headers)&&void 0!==i?i:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(o=l.auth)&&void 0!==o?o:{},this.headers,l.global.fetch),this.fetch=ju(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new wc(new URL("rest/v1",c).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new Ca(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new mu(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return Jl(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:n,storageKey:i,flowType:o,lock:s,debug:a},c,u){const l={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Kl({url:this.authUrl.href,headers:Object.assign(Object.assign({},l),c),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:n,flowType:o,lock:s,debug:a,fetch:u,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new Zc(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===r?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=r}}const Xl=(e,t,r)=>new Vl(e,t,r);export{Sa as C,$o as J,Uo as _,s as b,Xl as c,o as s};
